import React, { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import Icon from '../AppIcon';
import Button from './Button';

const Header = () => {
  const location = useLocation();
  const [isConnected, setIsConnected] = useState(true);
  const [lastUpdate, setLastUpdate] = useState(new Date());
  const [showEmergencyPanel, setShowEmergencyPanel] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  const navigationItems = [
    { path: '/dashboard', label: 'Dashboard', icon: 'BarChart3' },
    { path: '/gtt-orders-management', label: 'Orders', icon: 'TrendingUp' },
    { path: '/trade-log', label: 'Analysis', icon: 'PieChart' },
    { path: '/capital-management', label: 'Settings', icon: 'Settings' }
  ];

  const analysisItems = [
    { path: '/trade-log', label: 'Trade Log' },
    { path: '/boh-eligible-stocks', label: 'BOH Eligible Stocks' }
  ];

  const settingsItems = [
    { path: '/capital-management', label: 'Capital Management' },
    { path: '/settings-testing', label: 'Settings & Testing' }
  ];

  useEffect(() => {
    const interval = setInterval(() => {
      setLastUpdate(new Date());
    }, 30000);

    return () => clearInterval(interval);
  }, []);

  const getActiveTab = () => {
    if (location.pathname === '/dashboard') return 'Dashboard';
    if (location.pathname === '/gtt-orders-management') return 'Orders';
    if (['/trade-log', '/boh-eligible-stocks'].includes(location.pathname)) return 'Analysis';
    if (['/capital-management', '/settings-testing'].includes(location.pathname)) return 'Settings';
    return 'Dashboard';
  };

  const handleEmergencyStop = () => {
    setShowEmergencyPanel(true);
  };

  const confirmEmergencyStop = () => {
    console.log('Emergency stop activated');
    setShowEmergencyPanel(false);
  };

  const formatTime = (date) => {
    return date.toLocaleTimeString('en-IN', { 
      hour12: false, 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  return (
    <>
      <header className="fixed top-0 left-0 right-0 z-50 bg-card border-b border-border h-16">
        <div className="flex items-center justify-between h-full px-6">
          {/* Logo */}
          <div className="flex items-center">
            <Link to="/dashboard" className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                <Icon name="TrendingUp" size={20} color="white" />
              </div>
              <span className="text-xl font-semibold text-foreground">Darvas Trading</span>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            {navigationItems.map((item) => {
              const isActive = getActiveTab() === item.label;
              return (
                <div key={item.label} className="relative">
                  {(item.label === 'Analysis' || item.label === 'Settings') ? (
                    <div className="group">
                      <button className={`flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 ${
                        isActive 
                          ? 'text-primary bg-primary/10' :'text-muted-foreground hover:text-foreground hover:bg-muted'
                      }`}>
                        <Icon name={item.icon} size={16} />
                        <span>{item.label}</span>
                        <Icon name="ChevronDown" size={14} />
                      </button>
                      <div className="absolute top-full left-0 mt-1 w-48 bg-popover border border-border rounded-md shadow-trading-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
                        {(item.label === 'Analysis' ? analysisItems : settingsItems).map((subItem) => (
                          <Link
                            key={subItem.path}
                            to={subItem.path}
                            className={`block px-4 py-2 text-sm transition-colors duration-200 first:rounded-t-md last:rounded-b-md ${
                              location.pathname === subItem.path
                                ? 'text-primary bg-primary/10' :'text-popover-foreground hover:bg-muted'
                            }`}
                          >
                            {subItem.label}
                          </Link>
                        ))}
                      </div>
                    </div>
                  ) : (
                    <Link
                      to={item.path}
                      className={`flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 ${
                        isActive 
                          ? 'text-primary bg-primary/10' :'text-muted-foreground hover:text-foreground hover:bg-muted'
                      }`}
                    >
                      <Icon name={item.icon} size={16} />
                      <span>{item.label}</span>
                    </Link>
                  )}
                </div>
              );
            })}
          </nav>

          {/* Status & Controls */}
          <div className="flex items-center space-x-4">
            {/* Connection Status */}
            <div className="hidden sm:flex items-center space-x-2 text-xs">
              <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-success animate-pulse-trading' : 'bg-error'}`} />
              <span className="text-muted-foreground">
                {isConnected ? 'Live' : 'Disconnected'}
              </span>
              <span className="text-muted-foreground">•</span>
              <span className="text-muted-foreground font-data">
                {formatTime(lastUpdate)}
              </span>
            </div>

            {/* Emergency Stop */}
            <Button
              variant="destructive"
              size="sm"
              onClick={handleEmergencyStop}
              className="hidden sm:flex"
            >
              <Icon name="Square" size={14} />
              Stop
            </Button>

            {/* Mobile Menu Toggle */}
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
              className="md:hidden"
            >
              <Icon name={mobileMenuOpen ? "X" : "Menu"} size={20} />
            </Button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {mobileMenuOpen && (
          <div className="md:hidden bg-card border-t border-border">
            <nav className="px-4 py-2 space-y-1">
              {navigationItems.map((item) => (
                <div key={item.label}>
                  {(item.label === 'Analysis' || item.label === 'Settings') ? (
                    <div>
                      <div className="flex items-center space-x-2 px-3 py-2 text-sm font-medium text-muted-foreground">
                        <Icon name={item.icon} size={16} />
                        <span>{item.label}</span>
                      </div>
                      <div className="ml-6 space-y-1">
                        {(item.label === 'Analysis' ? analysisItems : settingsItems).map((subItem) => (
                          <Link
                            key={subItem.path}
                            to={subItem.path}
                            onClick={() => setMobileMenuOpen(false)}
                            className={`block px-3 py-2 text-sm rounded-md transition-colors duration-200 ${
                              location.pathname === subItem.path
                                ? 'text-primary bg-primary/10' :'text-foreground hover:bg-muted'
                            }`}
                          >
                            {subItem.label}
                          </Link>
                        ))}
                      </div>
                    </div>
                  ) : (
                    <Link
                      to={item.path}
                      onClick={() => setMobileMenuOpen(false)}
                      className={`flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 ${
                        getActiveTab() === item.label
                          ? 'text-primary bg-primary/10' :'text-foreground hover:bg-muted'
                      }`}
                    >
                      <Icon name={item.icon} size={16} />
                      <span>{item.label}</span>
                    </Link>
                  )}
                </div>
              ))}
              
              {/* Mobile Status & Emergency */}
              <div className="pt-4 mt-4 border-t border-border">
                <div className="flex items-center justify-between px-3 py-2">
                  <div className="flex items-center space-x-2 text-xs">
                    <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-success animate-pulse-trading' : 'bg-error'}`} />
                    <span className="text-muted-foreground">
                      {isConnected ? 'Live' : 'Disconnected'}
                    </span>
                    <span className="text-muted-foreground font-data">
                      {formatTime(lastUpdate)}
                    </span>
                  </div>
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={handleEmergencyStop}
                  >
                    <Icon name="Square" size={14} />
                    Stop
                  </Button>
                </div>
              </div>
            </nav>
          </div>
        )}
      </header>

      {/* Emergency Confirmation Modal */}
      {showEmergencyPanel && (
        <div className="fixed inset-0 z-[1300] bg-black/50 flex items-center justify-center p-4">
          <div className="bg-card rounded-lg shadow-trading-lg max-w-md w-full p-6">
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-10 h-10 bg-error/10 rounded-full flex items-center justify-center">
                <Icon name="AlertTriangle" size={20} color="var(--color-error)" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-foreground">Emergency Stop</h3>
                <p className="text-sm text-muted-foreground">This will halt all active strategies</p>
              </div>
            </div>
            <p className="text-sm text-foreground mb-6">
              Are you sure you want to stop all trading activities? This action will:
            </p>
            <ul className="text-sm text-muted-foreground mb-6 space-y-1">
              <li>• Cancel all pending GTT orders</li>
              <li>• Pause strategy execution</li>
              <li>• Require manual restart</li>
            </ul>
            <div className="flex space-x-3">
              <Button
                variant="outline"
                onClick={() => setShowEmergencyPanel(false)}
                className="flex-1"
              >
                Cancel
              </Button>
              <Button
                variant="destructive"
                onClick={confirmEmergencyStop}
                className="flex-1"
              >
                <Icon name="Square" size={16} />
                Stop All
              </Button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default Header;