import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const CapitalUsageTable = ({ capitalUsage, perStockLimit }) => {
  const [sortField, setSortField] = useState('allocation');
  const [sortDirection, setSortDirection] = useState('desc');
  const [expandedRows, setExpandedRows] = useState(new Set());

  const handleSort = (field) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('desc');
    }
  };

  const toggleRowExpansion = (stockSymbol) => {
    const newExpanded = new Set(expandedRows);
    if (newExpanded.has(stockSymbol)) {
      newExpanded.delete(stockSymbol);
    } else {
      newExpanded.add(stockSymbol);
    }
    setExpandedRows(newExpanded);
  };

  const sortedData = [...capitalUsage].sort((a, b) => {
    let aValue, bValue;
    
    switch (sortField) {
      case 'symbol':
        aValue = a.symbol;
        bValue = b.symbol;
        break;
      case 'allocation':
        aValue = a.allocatedAmount;
        bValue = b.allocatedAmount;
        break;
      case 'entries':
        aValue = a.entries;
        bValue = b.entries;
        break;
      case 'remaining':
        aValue = a.remainingCapacity;
        bValue = b.remainingCapacity;
        break;
      case 'percentage':
        aValue = a.utilizationPercentage;
        bValue = b.utilizationPercentage;
        break;
      default:
        return 0;
    }

    if (typeof aValue === 'string') {
      return sortDirection === 'asc' 
        ? aValue.localeCompare(bValue)
        : bValue.localeCompare(aValue);
    }
    
    return sortDirection === 'asc' ? aValue - bValue : bValue - aValue;
  });

  const getSortIcon = (field) => {
    if (sortField !== field) return 'ArrowUpDown';
    return sortDirection === 'asc' ? 'ArrowUp' : 'ArrowDown';
  };

  const getUtilizationColor = (percentage) => {
    if (percentage >= 90) return 'text-error';
    if (percentage >= 75) return 'text-warning';
    if (percentage >= 50) return 'text-accent';
    return 'text-success';
  };

  const getUtilizationBg = (percentage) => {
    if (percentage >= 90) return 'bg-error';
    if (percentage >= 75) return 'bg-warning';
    if (percentage >= 50) return 'bg-accent';
    return 'bg-success';
  };

  return (
    <div className="bg-card rounded-lg border border-border">
      <div className="p-6 border-b border-border">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-success/10 rounded-lg flex items-center justify-center">
            <Icon name="PieChart" size={20} color="var(--color-success)" />
          </div>
          <div>
            <h2 className="text-lg font-semibold text-foreground">Capital Usage Breakdown</h2>
            <p className="text-sm text-muted-foreground">
              Stock-wise capital allocation and utilization
            </p>
          </div>
        </div>
      </div>

      <div className="overflow-x-auto">
        <table className="w-full">
          <thead className="bg-muted/50">
            <tr>
              <th className="text-left p-4 text-sm font-medium text-muted-foreground">
                <button
                  onClick={() => handleSort('symbol')}
                  className="flex items-center space-x-1 hover:text-foreground transition-colors"
                >
                  <span>Stock</span>
                  <Icon name={getSortIcon('symbol')} size={14} />
                </button>
              </th>
              <th className="text-right p-4 text-sm font-medium text-muted-foreground">
                <button
                  onClick={() => handleSort('allocation')}
                  className="flex items-center space-x-1 hover:text-foreground transition-colors ml-auto"
                >
                  <span>Allocated</span>
                  <Icon name={getSortIcon('allocation')} size={14} />
                </button>
              </th>
              <th className="text-center p-4 text-sm font-medium text-muted-foreground">
                <button
                  onClick={() => handleSort('entries')}
                  className="flex items-center space-x-1 hover:text-foreground transition-colors mx-auto"
                >
                  <span>Entries</span>
                  <Icon name={getSortIcon('entries')} size={14} />
                </button>
              </th>
              <th className="text-right p-4 text-sm font-medium text-muted-foreground">
                <button
                  onClick={() => handleSort('remaining')}
                  className="flex items-center space-x-1 hover:text-foreground transition-colors ml-auto"
                >
                  <span>Remaining</span>
                  <Icon name={getSortIcon('remaining')} size={14} />
                </button>
              </th>
              <th className="text-center p-4 text-sm font-medium text-muted-foreground">
                <button
                  onClick={() => handleSort('percentage')}
                  className="flex items-center space-x-1 hover:text-foreground transition-colors mx-auto"
                >
                  <span>Usage</span>
                  <Icon name={getSortIcon('percentage')} size={14} />
                </button>
              </th>
              <th className="w-12 p-4"></th>
            </tr>
          </thead>
          <tbody>
            {sortedData.map((stock) => (
              <React.Fragment key={stock.symbol}>
                <tr className="border-t border-border hover:bg-muted/30 transition-colors">
                  <td className="p-4">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center">
                        <span className="text-xs font-bold text-primary">
                          {stock.symbol.substring(0, 2)}
                        </span>
                      </div>
                      <div>
                        <div className="font-medium text-foreground">{stock.symbol}</div>
                        <div className="text-xs text-muted-foreground">{stock.companyName}</div>
                      </div>
                    </div>
                  </td>
                  <td className="p-4 text-right">
                    <div className="font-data font-medium text-foreground">
                      ₹{stock.allocatedAmount.toLocaleString('en-IN')}
                    </div>
                  </td>
                  <td className="p-4 text-center">
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-accent/10 text-accent">
                      {stock.entries}/5
                    </span>
                  </td>
                  <td className="p-4 text-right">
                    <div className="font-data font-medium text-foreground">
                      ₹{stock.remainingCapacity.toLocaleString('en-IN')}
                    </div>
                  </td>
                  <td className="p-4">
                    <div className="flex items-center justify-center space-x-2">
                      <div className="w-16 bg-muted rounded-full h-2">
                        <div 
                          className={`h-2 rounded-full transition-all duration-300 ${getUtilizationBg(stock.utilizationPercentage)}`}
                          style={{ width: `${stock.utilizationPercentage}%` }}
                        />
                      </div>
                      <span className={`text-xs font-medium ${getUtilizationColor(stock.utilizationPercentage)}`}>
                        {stock.utilizationPercentage.toFixed(0)}%
                      </span>
                    </div>
                  </td>
                  <td className="p-4">
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => toggleRowExpansion(stock.symbol)}
                      className="w-8 h-8"
                    >
                      <Icon 
                        name={expandedRows.has(stock.symbol) ? "ChevronUp" : "ChevronDown"} 
                        size={16} 
                      />
                    </Button>
                  </td>
                </tr>
                
                {expandedRows.has(stock.symbol) && (
                  <tr className="border-t border-border bg-muted/20">
                    <td colSpan="6" className="p-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <div className="bg-card rounded-lg p-3 border border-border">
                          <div className="text-xs text-muted-foreground mb-1">Average Price</div>
                          <div className="font-data font-medium text-foreground">
                            ₹{stock.averagePrice.toFixed(2)}
                          </div>
                        </div>
                        <div className="bg-card rounded-lg p-3 border border-border">
                          <div className="text-xs text-muted-foreground mb-1">Total Quantity</div>
                          <div className="font-data font-medium text-foreground">
                            {stock.totalQuantity}
                          </div>
                        </div>
                        <div className="bg-card rounded-lg p-3 border border-border">
                          <div className="text-xs text-muted-foreground mb-1">Current P&L</div>
                          <div className={`font-data font-medium ${stock.currentPnL >= 0 ? 'text-success' : 'text-error'}`}>
                            {stock.currentPnL >= 0 ? '+' : ''}₹{stock.currentPnL.toLocaleString('en-IN')}
                          </div>
                        </div>
                        <div className="bg-card rounded-lg p-3 border border-border">
                          <div className="text-xs text-muted-foreground mb-1">Next Entry At</div>
                          <div className="font-data font-medium text-foreground">
                            ₹{stock.nextEntryPrice.toFixed(2)}
                          </div>
                        </div>
                      </div>
                    </td>
                  </tr>
                )}
              </React.Fragment>
            ))}
          </tbody>
        </table>
      </div>

      {sortedData.length === 0 && (
        <div className="p-8 text-center">
          <Icon name="PieChart" size={48} color="var(--color-muted-foreground)" className="mx-auto mb-4" />
          <h3 className="text-lg font-medium text-foreground mb-2">No Capital Allocated</h3>
          <p className="text-sm text-muted-foreground">
            Capital allocation will appear here once you start trading
          </p>
        </div>
      )}
    </div>
  );
};

export default CapitalUsageTable;