import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';
import Input from '../../../components/ui/Input';
import Select from '../../../components/ui/Select';
import { Checkbox } from '../../../components/ui/Checkbox';

const TestingMode = () => {
  const [testingEnabled, setTestingEnabled] = useState(false);
  const [selectedScenario, setSelectedScenario] = useState('');
  const [mockData, setMockData] = useState({
    stockSymbol: 'RELIANCE',
    currentPrice: 2456.75,
    weekHigh52: 2856.50,
    weekLow52: 2100.25,
    volume: 1250000,
    marketCap: 1650000
  });
  
  const [simulationSettings, setSimulationSettings] = useState({
    startDate: '2024-01-01',
    endDate: '2024-12-31',
    initialCapital: 100000,
    speedMultiplier: 1,
    includeWeekends: false,
    randomEvents: true
  });

  const [testResults, setTestResults] = useState([]);
  const [isRunningTest, setIsRunningTest] = useState(false);

  const testScenarios = [
    { value: 'bullish_breakout', label: 'Bullish Breakout Scenario' },
    { value: 'bearish_reversal', label: 'Bearish Reversal Scenario' },
    { value: 'sideways_market', label: 'Sideways Market Scenario' },
    { value: 'high_volatility', label: 'High Volatility Scenario' },
    { value: 'low_volume', label: 'Low Volume Scenario' },
    { value: 'gap_up_down', label: 'Gap Up/Down Scenario' },
    { value: 'custom', label: 'Custom Scenario' }
  ];

  const mockStocks = [
    { value: 'RELIANCE', label: 'Reliance Industries' },
    { value: 'TCS', label: 'Tata Consultancy Services' },
    { value: 'INFY', label: 'Infosys Limited' },
    { value: 'HDFCBANK', label: 'HDFC Bank' },
    { value: 'ICICIBANK', label: 'ICICI Bank' },
    { value: 'SBIN', label: 'State Bank of India' },
    { value: 'BHARTIARTL', label: 'Bharti Airtel' },
    { value: 'ITC', label: 'ITC Limited' }
  ];

  const handleMockDataChange = (key, value) => {
    setMockData(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleSimulationSettingChange = (key, value) => {
    setSimulationSettings(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const generateMockSignal = () => {
    const signal = {
      id: Date.now(),
      timestamp: new Date(),
      symbol: mockData.stockSymbol,
      type: Math.random() > 0.5 ? 'BUY' : 'SELL',
      price: mockData.currentPrice,
      quantity: Math.floor(Math.random() * 100) + 1,
      reason: 'Mock signal generated for testing',
      confidence: Math.floor(Math.random() * 40) + 60
    };

    setTestResults(prev => [signal, ...prev.slice(0, 9)]);
    console.log('Generated mock signal:', signal);
  };

  const runScenarioTest = async () => {
    if (!selectedScenario) return;
    
    setIsRunningTest(true);
    setTestResults([]);

    // Simulate test execution
    for (let i = 0; i < 5; i++) {
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const result = {
        id: Date.now() + i,
        timestamp: new Date(Date.now() - i * 60000),
        scenario: selectedScenario,
        action: ['Signal Generated', 'Order Placed', 'Order Executed', 'Position Closed'][Math.floor(Math.random() * 4)],
        symbol: mockData.stockSymbol,
        price: mockData.currentPrice + (Math.random() - 0.5) * 100,
        pnl: (Math.random() - 0.5) * 5000,
        status: Math.random() > 0.2 ? 'Success' : 'Failed'
      };

      setTestResults(prev => [result, ...prev]);
    }

    setIsRunningTest(false);
  };

  const clearTestResults = () => {
    setTestResults([]);
  };

  const exportTestResults = () => {
    const dataStr = JSON.stringify(testResults, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `test-results-${new Date().toISOString().split('T')[0]}.json`;
    link.click();
  };

  return (
    <div className="space-y-8">
      {/* Testing Mode Toggle */}
      <div className="bg-card rounded-lg border border-border p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${
              testingEnabled ? 'bg-warning/10' : 'bg-muted/50'
            }`}>
              <Icon 
                name="TestTube" 
                size={20} 
                color={testingEnabled ? "var(--color-warning)" : "var(--color-muted-foreground)"}
              />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-foreground">Testing Mode</h3>
              <p className="text-sm text-muted-foreground">
                {testingEnabled ? 'Testing mode is active - no real trades will be executed' : 'Enable testing mode to simulate trading scenarios'}
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-3">
            {testingEnabled && (
              <div className="flex items-center space-x-2 px-3 py-1 bg-warning/10 rounded-full">
                <div className="w-2 h-2 bg-warning rounded-full animate-pulse-trading" />
                <span className="text-xs font-medium text-warning">TEST MODE</span>
              </div>
            )}
            <Button
              variant={testingEnabled ? "warning" : "outline"}
              onClick={() => setTestingEnabled(!testingEnabled)}
              iconName={testingEnabled ? "Square" : "Play"}
              iconPosition="left"
            >
              {testingEnabled ? 'Stop Testing' : 'Start Testing'}
            </Button>
          </div>
        </div>

        {testingEnabled && (
          <div className="bg-warning/5 border border-warning/20 rounded-lg p-4">
            <div className="flex items-start space-x-3">
              <Icon name="AlertTriangle" size={20} color="var(--color-warning)" />
              <div>
                <h4 className="text-sm font-medium text-warning mb-1">Testing Mode Active</h4>
                <p className="text-xs text-muted-foreground">
                  All trading operations are simulated. No real orders will be placed or executed. 
                  Market data may be mocked for testing purposes.
                </p>
              </div>
            </div>
          </div>
        )}
      </div>

      {testingEnabled && (
        <>
          {/* Mock Data Configuration */}
          <div className="bg-card rounded-lg border border-border p-6">
            <div className="flex items-center space-x-3 mb-6">
              <div className="w-10 h-10 bg-accent/10 rounded-lg flex items-center justify-center">
                <Icon name="Database" size={20} color="var(--color-accent)" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-foreground">Mock Data Configuration</h3>
                <p className="text-sm text-muted-foreground">Configure mock market data for testing</p>
              </div>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="space-y-4">
                <Select
                  label="Stock Symbol"
                  options={mockStocks}
                  value={mockData.stockSymbol}
                  onChange={(value) => handleMockDataChange('stockSymbol', value)}
                />

                <Input
                  label="Current Price (₹)"
                  type="number"
                  value={mockData.currentPrice}
                  onChange={(e) => handleMockDataChange('currentPrice', parseFloat(e.target.value))}
                  step="0.01"
                />

                <Input
                  label="52 Week High (₹)"
                  type="number"
                  value={mockData.weekHigh52}
                  onChange={(e) => handleMockDataChange('weekHigh52', parseFloat(e.target.value))}
                  step="0.01"
                />

                <Input
                  label="52 Week Low (₹)"
                  type="number"
                  value={mockData.weekLow52}
                  onChange={(e) => handleMockDataChange('weekLow52', parseFloat(e.target.value))}
                  step="0.01"
                />
              </div>

              <div className="space-y-4">
                <Input
                  label="Volume"
                  type="number"
                  value={mockData.volume}
                  onChange={(e) => handleMockDataChange('volume', parseInt(e.target.value))}
                />

                <Input
                  label="Market Cap (₹ Cr)"
                  type="number"
                  value={mockData.marketCap}
                  onChange={(e) => handleMockDataChange('marketCap', parseInt(e.target.value))}
                />

                <div className="pt-4">
                  <Button
                    variant="outline"
                    onClick={generateMockSignal}
                    iconName="Zap"
                    iconPosition="left"
                    className="w-full"
                  >
                    Generate Mock Signal
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {/* Scenario Testing */}
          <div className="bg-card rounded-lg border border-border p-6">
            <div className="flex items-center space-x-3 mb-6">
              <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                <Icon name="PlayCircle" size={20} color="var(--color-primary)" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-foreground">Scenario Testing</h3>
                <p className="text-sm text-muted-foreground">Run predefined market scenarios</p>
              </div>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="space-y-4">
                <Select
                  label="Test Scenario"
                  placeholder="Select a scenario to test"
                  options={testScenarios}
                  value={selectedScenario}
                  onChange={setSelectedScenario}
                />

                <Input
                  label="Start Date"
                  type="date"
                  value={simulationSettings.startDate}
                  onChange={(e) => handleSimulationSettingChange('startDate', e.target.value)}
                />

                <Input
                  label="End Date"
                  type="date"
                  value={simulationSettings.endDate}
                  onChange={(e) => handleSimulationSettingChange('endDate', e.target.value)}
                />

                <Input
                  label="Initial Capital (₹)"
                  type="number"
                  value={simulationSettings.initialCapital}
                  onChange={(e) => handleSimulationSettingChange('initialCapital', parseInt(e.target.value))}
                />
              </div>

              <div className="space-y-4">
                <Input
                  label="Speed Multiplier"
                  type="number"
                  description="1x = Real time, 10x = 10 times faster"
                  value={simulationSettings.speedMultiplier}
                  onChange={(e) => handleSimulationSettingChange('speedMultiplier', parseInt(e.target.value))}
                  min="1"
                  max="100"
                />

                <Checkbox
                  label="Include Weekends"
                  description="Process signals during weekends"
                  checked={simulationSettings.includeWeekends}
                  onChange={(e) => handleSimulationSettingChange('includeWeekends', e.target.checked)}
                />

                <Checkbox
                  label="Random Market Events"
                  description="Include random market events in simulation"
                  checked={simulationSettings.randomEvents}
                  onChange={(e) => handleSimulationSettingChange('randomEvents', e.target.checked)}
                />

                <div className="pt-2">
                  <Button
                    variant="default"
                    onClick={runScenarioTest}
                    disabled={!selectedScenario || isRunningTest}
                    loading={isRunningTest}
                    iconName="Play"
                    iconPosition="left"
                    className="w-full"
                  >
                    {isRunningTest ? 'Running Test...' : 'Run Scenario Test'}
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {/* Test Results */}
          <div className="bg-card rounded-lg border border-border p-6">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-success/10 rounded-lg flex items-center justify-center">
                  <Icon name="BarChart3" size={20} color="var(--color-success)" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-foreground">Test Results</h3>
                  <p className="text-sm text-muted-foreground">
                    {testResults.length} test results
                  </p>
                </div>
              </div>
              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={exportTestResults}
                  disabled={testResults.length === 0}
                  iconName="Download"
                  iconPosition="left"
                >
                  Export
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={clearTestResults}
                  disabled={testResults.length === 0}
                  iconName="Trash2"
                  iconPosition="left"
                >
                  Clear
                </Button>
              </div>
            </div>

            {testResults.length > 0 ? (
              <div className="space-y-3 max-h-96 overflow-y-auto">
                {testResults.map((result) => (
                  <div key={result.id} className="bg-muted/50 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center space-x-3">
                        <div className={`w-2 h-2 rounded-full ${
                          result.status === 'Success' ? 'bg-success' : 'bg-error'
                        }`} />
                        <span className="text-sm font-medium">{result.action || result.type}</span>
                        {result.symbol && (
                          <span className="text-xs bg-primary/10 text-primary px-2 py-1 rounded">
                            {result.symbol}
                          </span>
                        )}
                      </div>
                      <span className="text-xs text-muted-foreground">
                        {result.timestamp.toLocaleTimeString('en-IN')}
                      </span>
                    </div>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-xs">
                      {result.price && (
                        <div>
                          <span className="text-muted-foreground">Price: </span>
                          <span className="font-data">₹{result.price.toFixed(2)}</span>
                        </div>
                      )}
                      {result.quantity && (
                        <div>
                          <span className="text-muted-foreground">Qty: </span>
                          <span className="font-data">{result.quantity}</span>
                        </div>
                      )}
                      {result.pnl !== undefined && (
                        <div>
                          <span className="text-muted-foreground">P&L: </span>
                          <span className={`font-data ${result.pnl >= 0 ? 'text-success' : 'text-error'}`}>
                            ₹{result.pnl.toFixed(2)}
                          </span>
                        </div>
                      )}
                      {result.confidence && (
                        <div>
                          <span className="text-muted-foreground">Confidence: </span>
                          <span className="font-data">{result.confidence}%</span>
                        </div>
                      )}
                    </div>
                    {result.reason && (
                      <p className="text-xs text-muted-foreground mt-2">{result.reason}</p>
                    )}
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <Icon name="FileText" size={48} className="text-muted-foreground mx-auto mb-4" />
                <p className="text-muted-foreground">No test results yet</p>
                <p className="text-sm text-muted-foreground">Run a scenario test or generate mock signals to see results</p>
              </div>
            )}
          </div>
        </>
      )}
    </div>
  );
};

export default TestingMode;