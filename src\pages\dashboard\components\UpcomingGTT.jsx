import React from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const UpcomingGTT = ({ gttOrders }) => {
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const formatPrice = (price) => {
    return new Intl.NumberFormat('en-IN', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(price);
  };

  const getOrderTypeColor = (type) => {
    return type === 'BUY' ? 'text-success' : 'text-error';
  };

  const getOrderTypeBg = (type) => {
    return type === 'BUY' ? 'bg-success/10' : 'bg-error/10';
  };

  const calculatePercentageFromCurrent = (triggerPrice, currentPrice) => {
    return ((triggerPrice - currentPrice) / currentPrice * 100);
  };

  return (
    <div className="bg-card border border-border rounded-lg shadow-trading">
      <div className="p-6 border-b border-border">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-foreground">Upcoming GTT Orders</h3>
            <p className="text-sm text-muted-foreground">
              {gttOrders?.length || 0} pending orders
            </p>
          </div>
          <Button variant="outline" size="sm" iconName="Settings">
            Manage
          </Button>
        </div>
      </div>

      <div className="p-6">
        {gttOrders && gttOrders.length > 0 ? (
          <div className="space-y-4">
            {gttOrders.map((order, index) => {
              const percentageFromCurrent = calculatePercentageFromCurrent(
                order.triggerPrice, 
                order.currentPrice
              );
              
              return (
                <div key={index} className="border border-border rounded-lg p-4 hover:bg-muted/30 transition-colors">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center">
                        <span className="text-xs font-bold text-primary">
                          {order.symbol.substring(0, 2)}
                        </span>
                      </div>
                      <div>
                        <div className="font-medium text-foreground">{order.symbol}</div>
                        <div className="text-xs text-muted-foreground">{order.exchange}</div>
                      </div>
                    </div>
                    
                    <div className={`px-2 py-1 rounded-full text-xs font-medium ${getOrderTypeBg(order.type)} ${getOrderTypeColor(order.type)}`}>
                      {order.type}
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4 mb-3">
                    <div>
                      <div className="text-xs text-muted-foreground mb-1">Trigger Price</div>
                      <div className="font-data text-foreground">₹{formatPrice(order.triggerPrice)}</div>
                    </div>
                    <div>
                      <div className="text-xs text-muted-foreground mb-1">Quantity</div>
                      <div className="font-data text-foreground">{order.quantity}</div>
                    </div>
                    <div>
                      <div className="text-xs text-muted-foreground mb-1">Current Price</div>
                      <div className="font-data text-foreground">₹{formatPrice(order.currentPrice)}</div>
                    </div>
                    <div>
                      <div className="text-xs text-muted-foreground mb-1">Order Value</div>
                      <div className="font-data text-foreground">
                        {formatCurrency(order.triggerPrice * order.quantity)}
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Icon 
                        name={percentageFromCurrent >= 0 ? "TrendingUp" : "TrendingDown"} 
                        size={14} 
                        color={percentageFromCurrent >= 0 ? "var(--color-success)" : "var(--color-error)"}
                      />
                      <span className={`text-sm font-data ${
                        percentageFromCurrent >= 0 ? 'text-success' : 'text-error'
                      }`}>
                        {percentageFromCurrent >= 0 ? '+' : ''}{percentageFromCurrent.toFixed(2)}%
                      </span>
                      <span className="text-xs text-muted-foreground">from current</span>
                    </div>
                    
                    <div className="flex items-center space-x-1">
                      <Button variant="ghost" size="icon" className="w-8 h-8">
                        <Icon name="Edit" size={14} />
                      </Button>
                      <Button variant="ghost" size="icon" className="w-8 h-8">
                        <Icon name="Trash2" size={14} />
                      </Button>
                    </div>
                  </div>
                  
                  {order.condition && (
                    <div className="mt-3 p-2 bg-muted/50 rounded text-xs text-muted-foreground">
                      <Icon name="Info" size={12} className="inline mr-1" />
                      {order.condition}
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        ) : (
          <div className="text-center py-8">
            <Icon name="Clock" size={48} color="var(--color-muted-foreground)" className="mx-auto mb-4" />
            <h4 className="text-lg font-medium text-foreground mb-2">No Pending Orders</h4>
            <p className="text-muted-foreground mb-4">
              GTT orders will appear here when signals are generated.
            </p>
            <Button variant="outline" iconName="Plus" iconPosition="left">
              Create Manual GTT
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

export default UpcomingGTT;