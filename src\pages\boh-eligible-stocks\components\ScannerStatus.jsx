import React, { useState, useEffect } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const ScannerStatus = ({ onManualScan, isScanning }) => {
  const [lastScanTime, setLastScanTime] = useState(new Date('2025-01-30T20:00:00'));
  const [nextScanTime, setNextScanTime] = useState(new Date('2025-02-07T20:00:00'));
  const [currentTime, setCurrentTime] = useState(new Date());
  const [scanProgress, setScanProgress] = useState(0);

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  useEffect(() => {
    if (isScanning) {
      const progressTimer = setInterval(() => {
        setScanProgress(prev => {
          if (prev >= 100) {
            clearInterval(progressTimer);
            return 100;
          }
          return prev + Math.random() * 10;
        });
      }, 500);

      return () => clearInterval(progressTimer);
    } else {
      setScanProgress(0);
    }
  }, [isScanning]);

  const formatDateTime = (date) => {
    return date.toLocaleString('en-IN', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    });
  };

  const getTimeUntilNextScan = () => {
    const diff = nextScanTime.getTime() - currentTime.getTime();
    if (diff <= 0) return 'Overdue';
    
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    
    if (days > 0) return `${days}d ${hours}h ${minutes}m`;
    if (hours > 0) return `${hours}h ${minutes}m`;
    return `${minutes}m`;
  };

  const getScanStatus = () => {
    if (isScanning) return 'scanning';
    
    const timeSinceLastScan = currentTime.getTime() - lastScanTime.getTime();
    const oneWeek = 7 * 24 * 60 * 60 * 1000;
    
    if (timeSinceLastScan > oneWeek) return 'overdue';
    if (timeSinceLastScan > oneWeek * 0.8) return 'due-soon';
    return 'current';
  };

  const getStatusColor = () => {
    const status = getScanStatus();
    switch (status) {
      case 'scanning': return 'text-accent';
      case 'overdue': return 'text-error';
      case 'due-soon': return 'text-warning';
      default: return 'text-success';
    }
  };

  const getStatusIcon = () => {
    const status = getScanStatus();
    switch (status) {
      case 'scanning': return 'RefreshCw';
      case 'overdue': return 'AlertTriangle';
      case 'due-soon': return 'Clock';
      default: return 'CheckCircle';
    }
  };

  const getStatusText = () => {
    const status = getScanStatus();
    switch (status) {
      case 'scanning': return 'Scanning in progress...';
      case 'overdue': return 'Scan overdue';
      case 'due-soon': return 'Scan due soon';
      default: return 'Scanner active';
    }
  };

  return (
    <div className="bg-card rounded-lg border border-border p-6 mb-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
            isScanning ? 'bg-accent/10' : 'bg-success/10'
          }`}>
            <Icon 
              name={getStatusIcon()} 
              size={20} 
              color={`var(--color-${isScanning ? 'accent' : 'success'})`}
              className={isScanning ? 'animate-spin' : ''}
            />
          </div>
          <div>
            <h2 className="text-lg font-semibold text-foreground">Weekly High Signal Generator</h2>
            <p className={`text-sm font-medium ${getStatusColor()}`}>
              {getStatusText()}
            </p>
          </div>
        </div>
        <Button
          variant="outline"
          onClick={onManualScan}
          disabled={isScanning}
        >
          <Icon name="Search" size={16} />
          {isScanning ? 'Scanning...' : 'Manual Scan'}
        </Button>
      </div>

      {/* Scan Progress */}
      {isScanning && (
        <div className="mb-6">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm text-muted-foreground">Scanning stocks...</span>
            <span className="text-sm font-data text-foreground">{Math.round(scanProgress)}%</span>
          </div>
          <div className="w-full bg-muted rounded-full h-2">
            <div 
              className="bg-accent h-2 rounded-full transition-all duration-300 ease-out"
              style={{ width: `${scanProgress}%` }}
            />
          </div>
        </div>
      )}

      {/* Scan Information Grid */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Last Scan */}
        <div className="bg-muted/30 rounded-lg p-4">
          <div className="flex items-center space-x-2 mb-2">
            <Icon name="History" size={16} color="var(--color-muted-foreground)" />
            <span className="text-sm font-medium text-muted-foreground">Last Scan</span>
          </div>
          <div className="space-y-1">
            <p className="text-sm font-data text-foreground">
              {formatDateTime(lastScanTime)}
            </p>
            <p className="text-xs text-muted-foreground">
              Friday 8:00 PM (Automated)
            </p>
          </div>
        </div>

        {/* Next Scan */}
        <div className="bg-muted/30 rounded-lg p-4">
          <div className="flex items-center space-x-2 mb-2">
            <Icon name="Calendar" size={16} color="var(--color-muted-foreground)" />
            <span className="text-sm font-medium text-muted-foreground">Next Scan</span>
          </div>
          <div className="space-y-1">
            <p className="text-sm font-data text-foreground">
              {formatDateTime(nextScanTime)}
            </p>
            <p className="text-xs text-muted-foreground">
              In {getTimeUntilNextScan()}
            </p>
          </div>
        </div>

        {/* Scan Settings */}
        <div className="bg-muted/30 rounded-lg p-4">
          <div className="flex items-center space-x-2 mb-2">
            <Icon name="Settings" size={16} color="var(--color-muted-foreground)" />
            <span className="text-sm font-medium text-muted-foreground">Configuration</span>
          </div>
          <div className="space-y-1">
            <p className="text-sm text-foreground">
              Auto-scan: <span className="font-medium text-success">Enabled</span>
            </p>
            <p className="text-xs text-muted-foreground">
              Every Friday at 8:00 PM
            </p>
          </div>
        </div>
      </div>

      {/* Scan Results Summary */}
      <div className="mt-6 pt-6 border-t border-border">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-foreground">1,247</div>
            <div className="text-xs text-muted-foreground uppercase tracking-wide">Total Scanned</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-success">23</div>
            <div className="text-xs text-muted-foreground uppercase tracking-wide">Eligible</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-warning">47</div>
            <div className="text-xs text-muted-foreground uppercase tracking-wide">Near Eligible</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-accent">8</div>
            <div className="text-xs text-muted-foreground uppercase tracking-wide">New Signals</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ScannerStatus;