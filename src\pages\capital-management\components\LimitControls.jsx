import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Input from '../../../components/ui/Input';
import Button from '../../../components/ui/Button';

const LimitControls = ({ 
  perStockLimit, 
  perTradeLimit, 
  maxEntriesPerStock,
  onLimitsChange 
}) => {
  const [stockLimit, setStockLimit] = useState(perStockLimit.toString());
  const [tradeLimit, setTradeLimit] = useState(perTradeLimit.toString());
  const [maxEntries, setMaxEntries] = useState(maxEntriesPerStock.toString());
  const [isEditing, setIsEditing] = useState(false);
  const [errors, setErrors] = useState({});

  const validateLimits = () => {
    const newErrors = {};
    
    const stockLimitNum = parseFloat(stockLimit);
    const tradeLimitNum = parseFloat(tradeLimit);
    const maxEntriesNum = parseInt(maxEntries);

    if (isNaN(stockLimitNum) || stockLimitNum <= 0 || stockLimitNum > 10000) {
      newErrors.stockLimit = 'Stock limit must be between ₹1 and ₹10,000';
    }

    if (isNaN(tradeLimitNum) || tradeLimitNum <= 0 || tradeLimitNum > 2000) {
      newErrors.tradeLimit = 'Trade limit must be between ₹1 and ₹2,000';
    }

    if (isNaN(maxEntriesNum) || maxEntriesNum <= 0 || maxEntriesNum > 5) {
      newErrors.maxEntries = 'Max entries must be between 1 and 5';
    }

    if (tradeLimitNum * maxEntriesNum > stockLimitNum) {
      newErrors.general = 'Total possible investment per stock exceeds stock limit';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = () => {
    if (validateLimits()) {
      onLimitsChange({
        perStockLimit: parseFloat(stockLimit),
        perTradeLimit: parseFloat(tradeLimit),
        maxEntriesPerStock: parseInt(maxEntries)
      });
      setIsEditing(false);
    }
  };

  const handleCancel = () => {
    setStockLimit(perStockLimit.toString());
    setTradeLimit(perTradeLimit.toString());
    setMaxEntries(maxEntriesPerStock.toString());
    setIsEditing(false);
    setErrors({});
  };

  const presetConfigs = [
    { name: 'Conservative', stock: 5000, trade: 1000, entries: 3 },
    { name: 'Moderate', stock: 7500, trade: 1500, entries: 4 },
    { name: 'Aggressive', stock: 10000, trade: 2000, entries: 5 }
  ];

  const applyPreset = (preset) => {
    setStockLimit(preset.stock.toString());
    setTradeLimit(preset.trade.toString());
    setMaxEntries(preset.entries.toString());
  };

  return (
    <div className="bg-card rounded-lg border border-border p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-accent/10 rounded-lg flex items-center justify-center">
            <Icon name="Settings" size={20} color="var(--color-accent)" />
          </div>
          <div>
            <h2 className="text-lg font-semibold text-foreground">Trading Limits</h2>
            <p className="text-sm text-muted-foreground">Configure per-stock and per-trade limits</p>
          </div>
        </div>
        {!isEditing && (
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsEditing(true)}
            iconName="Edit2"
            iconPosition="left"
          >
            Edit Limits
          </Button>
        )}
      </div>

      {isEditing ? (
        <div className="space-y-6">
          {/* Preset Templates */}
          <div className="space-y-3">
            <h3 className="text-sm font-medium text-foreground">Quick Presets</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
              {presetConfigs.map((preset) => (
                <Button
                  key={preset.name}
                  variant="outline"
                  size="sm"
                  onClick={() => applyPreset(preset)}
                  className="justify-start"
                >
                  <div className="text-left">
                    <div className="font-medium">{preset.name}</div>
                    <div className="text-xs text-muted-foreground">
                      ₹{preset.stock.toLocaleString('en-IN')}/stock, ₹{preset.trade.toLocaleString('en-IN')}/trade
                    </div>
                  </div>
                </Button>
              ))}
            </div>
          </div>

          {/* Error Message */}
          {errors.general && (
            <div className="bg-error/10 border border-error/20 rounded-lg p-3">
              <div className="flex items-center space-x-2">
                <Icon name="AlertTriangle" size={16} color="var(--color-error)" />
                <span className="text-sm text-error">{errors.general}</span>
              </div>
            </div>
          )}

          {/* Limit Inputs */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Input
              label="Max Per Stock"
              type="number"
              placeholder="10000"
              value={stockLimit}
              onChange={(e) => setStockLimit(e.target.value)}
              error={errors.stockLimit}
              description="Maximum ₹10,000 per stock"
              required
            />
            
            <Input
              label="Max Per Trade"
              type="number"
              placeholder="2000"
              value={tradeLimit}
              onChange={(e) => setTradeLimit(e.target.value)}
              error={errors.tradeLimit}
              description="Maximum ₹2,000 per entry"
              required
            />
            
            <Input
              label="Max Entries"
              type="number"
              placeholder="5"
              value={maxEntries}
              onChange={(e) => setMaxEntries(e.target.value)}
              error={errors.maxEntries}
              description="Maximum 5 entries per stock"
              required
            />
          </div>

          <div className="flex space-x-3">
            <Button
              variant="default"
              size="sm"
              onClick={handleSave}
              disabled={Object.keys(errors).length > 0}
              iconName="Check"
              iconPosition="left"
            >
              Save Limits
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleCancel}
              iconName="X"
              iconPosition="left"
            >
              Cancel
            </Button>
          </div>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-muted/50 rounded-lg p-4">
            <div className="flex items-center space-x-2 mb-2">
              <Icon name="Building2" size={16} color="var(--color-primary)" />
              <span className="text-sm text-muted-foreground">Per Stock Limit</span>
            </div>
            <div className="text-2xl font-bold text-foreground font-data">
              ₹{perStockLimit.toLocaleString('en-IN')}
            </div>
            <div className="text-xs text-muted-foreground mt-1">
              Maximum investment per stock
            </div>
          </div>

          <div className="bg-muted/50 rounded-lg p-4">
            <div className="flex items-center space-x-2 mb-2">
              <Icon name="ArrowUpDown" size={16} color="var(--color-accent)" />
              <span className="text-sm text-muted-foreground">Per Trade Limit</span>
            </div>
            <div className="text-2xl font-bold text-foreground font-data">
              ₹{perTradeLimit.toLocaleString('en-IN')}
            </div>
            <div className="text-xs text-muted-foreground mt-1">
              Maximum amount per entry
            </div>
          </div>

          <div className="bg-muted/50 rounded-lg p-4">
            <div className="flex items-center space-x-2 mb-2">
              <Icon name="Hash" size={16} color="var(--color-success)" />
              <span className="text-sm text-muted-foreground">Max Entries</span>
            </div>
            <div className="text-2xl font-bold text-foreground font-data">
              {maxEntriesPerStock}
            </div>
            <div className="text-xs text-muted-foreground mt-1">
              Maximum entries per stock
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default LimitControls;