import { supabase } from './supabase';

const tradingService = {
  // Capital Management
  getCapitalTracker: async (userId) => {
    try {
      const { data, error } = await supabase
        .from('capital_tracker')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (error) {
        return { success: false, error: error.message };
      }

      return { success: true, data };
    } catch (error) {
      if (error?.message?.includes('Failed to fetch') || 
          error?.message?.includes('NetworkError') ||
          error?.name === 'TypeError' && error?.message?.includes('fetch')) {
        return { 
          success: false, 
          error: 'Cannot connect to database. Your Supabase project may be paused or deleted. Please visit your Supabase dashboard to check project status.' 
        };
      }
      return { success: false, error: 'Failed to load capital data' };
    }
  },

  updateCapitalSettings: async (userId, settings) => {
    try {
      const { data, error } = await supabase
        .from('capital_tracker')
        .update({
          ...settings,
          last_updated: new Date().toISOString()
        })
        .eq('user_id', userId)
        .select()
        .single();

      if (error) {
        return { success: false, error: error.message };
      }

      return { success: true, data };
    } catch (error) {
      return { success: false, error: 'Failed to update capital settings' };
    }
  },

  updateStrategyStatus: async (userId, status) => {
    try {
      const { data, error } = await supabase
        .from('capital_tracker')
        .update({
          strategy_status: status,
          last_updated: new Date().toISOString()
        })
        .eq('user_id', userId)
        .select()
        .single();

      if (error) {
        return { success: false, error: error.message };
      }

      return { success: true, data };
    } catch (error) {
      return { success: false, error: 'Failed to update strategy status' };
    }
  },

  // BOH Eligible Stocks
  getBOHEligibleStocks: async (filters = {}) => {
    try {
      let query = supabase
        .from('boh_eligible_stocks')
        .select('*')
        .order('last_scanned', { ascending: false });

      if (filters.eligibleOnly) {
        query = query.eq('is_boh_eligible', true);
      }

      if (filters.symbol) {
        query = query.ilike('symbol', `%${filters.symbol}%`);
      }

      const { data, error } = await query;

      if (error) {
        return { success: false, error: error.message };
      }

      return { success: true, data };
    } catch (error) {
      return { success: false, error: 'Failed to load BOH eligible stocks' };
    }
  },

  // Weekly Signals
  getWeeklySignals: async (filters = {}) => {
    try {
      let query = supabase
        .from('weekly_signals')
        .select('*')
        .order('signal_generated_at', { ascending: false });

      if (filters.unprocessedOnly) {
        query = query.eq('is_processed', false);
      }

      if (filters.symbol) {
        query = query.ilike('symbol', `%${filters.symbol}%`);
      }

      const { data, error } = await query;

      if (error) {
        return { success: false, error: error.message };
      }

      return { success: true, data };
    } catch (error) {
      return { success: false, error: 'Failed to load weekly signals' };
    }
  },

  // GTT Orders
  getGTTOrders: async (userId, filters = {}) => {
    try {
      let query = supabase
        .from('gtt_orders')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (filters.status) {
        query = query.eq('status', filters.status);
      }

      if (filters.orderType) {
        query = query.eq('order_type', filters.orderType);
      }

      const { data, error } = await query;

      if (error) {
        return { success: false, error: error.message };
      }

      return { success: true, data };
    } catch (error) {
      return { success: false, error: 'Failed to load GTT orders' };
    }
  },

  createGTTOrder: async (orderData) => {
    try {
      const { data, error } = await supabase
        .from('gtt_orders')
        .insert([orderData])
        .select()
        .single();

      if (error) {
        return { success: false, error: error.message };
      }

      return { success: true, data };
    } catch (error) {
      return { success: false, error: 'Failed to create GTT order' };
    }
  },

  updateGTTOrder: async (orderId, updates) => {
    try {
      const { data, error } = await supabase
        .from('gtt_orders')
        .update({
          ...updates,
          updated_at: new Date().toISOString()
        })
        .eq('id', orderId)
        .select()
        .single();

      if (error) {
        return { success: false, error: error.message };
      }

      return { success: true, data };
    } catch (error) {
      return { success: false, error: 'Failed to update GTT order' };
    }
  },

  cancelGTTOrder: async (orderId) => {
    try {
      const { data, error } = await supabase
        .from('gtt_orders')
        .update({
          status: 'cancelled',
          cancelled_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', orderId)
        .select()
        .single();

      if (error) {
        return { success: false, error: error.message };
      }

      return { success: true, data };
    } catch (error) {
      return { success: false, error: 'Failed to cancel GTT order' };
    }
  },

  // Current Holdings
  getCurrentHoldings: async (userId) => {
    try {
      const { data, error } = await supabase
        .from('current_holdings')
        .select('*')
        .eq('user_id', userId)
        .gt('total_quantity', 0)
        .order('updated_at', { ascending: false });

      if (error) {
        return { success: false, error: error.message };
      }

      return { success: true, data };
    } catch (error) {
      return { success: false, error: 'Failed to load holdings' };
    }
  },

  // Trade Log
  getTradeLog: async (userId, filters = {}) => {
    try {
      let query = supabase
        .from('trade_log')
        .select('*')
        .eq('user_id', userId)
        .order('trade_date', { ascending: false });

      if (filters.symbol) {
        query = query.ilike('symbol', `%${filters.symbol}%`);
      }

      if (filters.tradeType) {
        query = query.eq('trade_type', filters.tradeType);
      }

      if (filters.dateFrom) {
        query = query.gte('trade_date', filters.dateFrom);
      }

      if (filters.dateTo) {
        query = query.lte('trade_date', filters.dateTo);
      }

      const { data, error } = await query;

      if (error) {
        return { success: false, error: error.message };
      }

      return { success: true, data };
    } catch (error) {
      return { success: false, error: 'Failed to load trade log' };
    }
  },

  createTradeEntry: async (tradeData) => {
    try {
      const { data, error } = await supabase
        .from('trade_log')
        .insert([tradeData])
        .select()
        .single();

      if (error) {
        return { success: false, error: error.message };
      }

      // Update holdings summary after trade
      if (tradeData.user_id && tradeData.symbol) {
        await supabase.rpc('update_holding_summary', {
          holding_user_id: tradeData.user_id,
          holding_symbol: tradeData.symbol
        });

        // Update capital usage
        await supabase.rpc('update_capital_usage', {
          capital_user_id: tradeData.user_id
        });
      }

      return { success: true, data };
    } catch (error) {
      return { success: false, error: 'Failed to create trade entry' };
    }
  },

  // Market Data Integration (Yahoo Finance API substitute)
  fetchMarketData: async (symbol) => {
    try {
      // This would normally call Yahoo Finance API
      // For now, return mock data structure that matches expected format
      const mockData = {
        symbol: symbol,
        currentPrice: Math.random() * 1000 + 1000,
        open: Math.random() * 1000 + 1000,
        high: Math.random() * 1000 + 1000,
        low: Math.random() * 1000 + 1000,
        volume: Math.floor(Math.random() * 1000000),
        timestamp: new Date().toISOString()
      };

      return { success: true, data: mockData };
    } catch (error) {
      return { success: false, error: 'Failed to fetch market data' };
    }
  },

  // OHLC History
  getOHLCHistory: async (symbol, dateFrom, dateTo) => {
    try {
      let query = supabase
        .from('ohlc_history')
        .select('*')
        .eq('symbol', symbol)
        .order('trade_date', { ascending: true });

      if (dateFrom) {
        query = query.gte('trade_date', dateFrom);
      }

      if (dateTo) {
        query = query.lte('trade_date', dateTo);
      }

      const { data, error } = await query;

      if (error) {
        return { success: false, error: error.message };
      }

      return { success: true, data };
    } catch (error) {
      return { success: false, error: 'Failed to load OHLC history' };
    }
  },

  // Real-time subscriptions
  subscribeToGTTOrders: (userId, callback) => {
    return supabase
      .channel('gtt_orders')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'gtt_orders',
          filter: `user_id=eq.${userId}`
        },
        callback
      )
      .subscribe();
  },

  subscribeToHoldings: (userId, callback) => {
    return supabase
      .channel('holdings')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'current_holdings',
          filter: `user_id=eq.${userId}`
        },
        callback
      )
      .subscribe();
  },

  subscribeToTradeLog: (userId, callback) => {
    return supabase
      .channel('trade_log')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'trade_log',
          filter: `user_id=eq.${userId}`
        },
        callback
      )
      .subscribe();
  }
};

export default tradingService;