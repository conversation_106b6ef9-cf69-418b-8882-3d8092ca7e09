import React, { useState } from 'react';
import Header from '../../components/ui/Header';
import Sidebar from '../../components/ui/Sidebar';
import Icon from '../../components/AppIcon';
import Button from '../../components/ui/Button';
import StrategySettings from './components/StrategySettings';
import TestingMode from './components/TestingMode';
import SystemMonitoring from './components/SystemMonitoring';
import NotificationSettings from './components/NotificationSettings';
import DatabaseManagement from './components/DatabaseManagement';

const SettingsTesting = () => {
  const [activeTab, setActiveTab] = useState('strategy');

  const tabs = [
    {
      id: 'strategy',
      label: 'Strategy Settings',
      icon: 'Settings',
      description: 'Configure trading strategy parameters'
    },
    {
      id: 'testing',
      label: 'Testing Mode',
      icon: 'TestTube',
      description: 'Simulate trading scenarios and test strategies'
    },
    {
      id: 'monitoring',
      label: 'System Monitoring',
      icon: 'Activity',
      description: 'Monitor system performance and logs'
    },
    {
      id: 'notifications',
      label: 'Notifications',
      icon: 'Bell',
      description: 'Configure alerts and notification preferences'
    },
    {
      id: 'database',
      label: 'Database Management',
      icon: 'Database',
      description: 'Manage data backup, cleanup, and import/export'
    }
  ];

  const renderTabContent = () => {
    switch (activeTab) {
      case 'strategy':
        return <StrategySettings />;
      case 'testing':
        return <TestingMode />;
      case 'monitoring':
        return <SystemMonitoring />;
      case 'notifications':
        return <NotificationSettings />;
      case 'database':
        return <DatabaseManagement />;
      default:
        return <StrategySettings />;
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <Header />
      <Sidebar />
      
      <main className="pt-16 lg:ml-80">
        <div className="p-6">
          {/* Page Header */}
          <div className="mb-8">
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center">
                <Icon name="Settings" size={24} color="var(--color-primary)" />
              </div>
              <div>
                <h1 className="text-2xl font-semibold text-foreground">Settings & Testing</h1>
                <p className="text-muted-foreground">
                  Configure system settings and test trading strategies
                </p>
              </div>
            </div>

            {/* Quick Actions */}
            <div className="flex flex-wrap gap-3">
              <Button
                variant="outline"
                size="sm"
                iconName="Save"
                iconPosition="left"
                onClick={() => console.log('Save all settings')}
              >
                Save All Settings
              </Button>
              <Button
                variant="outline"
                size="sm"
                iconName="RotateCcw"
                iconPosition="left"
                onClick={() => console.log('Reset to defaults')}
              >
                Reset to Defaults
              </Button>
              <Button
                variant="outline"
                size="sm"
                iconName="Download"
                iconPosition="left"
                onClick={() => console.log('Export settings')}
              >
                Export Settings
              </Button>
            </div>
          </div>

          {/* Tab Navigation */}
          <div className="bg-card rounded-lg border border-border mb-6">
            <div className="border-b border-border">
              <nav className="flex overflow-x-auto">
                {tabs.map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`flex items-center space-x-3 px-6 py-4 text-sm font-medium whitespace-nowrap border-b-2 transition-colors duration-200 ${
                      activeTab === tab.id
                        ? 'border-primary text-primary bg-primary/5' :'border-transparent text-muted-foreground hover:text-foreground hover:bg-muted/50'
                    }`}
                  >
                    <Icon name={tab.icon} size={18} />
                    <div className="text-left">
                      <div>{tab.label}</div>
                      <div className="text-xs text-muted-foreground hidden sm:block">
                        {tab.description}
                      </div>
                    </div>
                  </button>
                ))}
              </nav>
            </div>

            {/* Tab Content */}
            <div className="p-6">
              {renderTabContent()}
            </div>
          </div>

          {/* System Status Footer */}
          <div className="bg-card rounded-lg border border-border p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-success rounded-full animate-pulse-trading" />
                  <span className="text-sm text-muted-foreground">System Status: Operational</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Icon name="Clock" size={14} className="text-muted-foreground" />
                  <span className="text-sm text-muted-foreground">
                    Last Updated: {new Date().toLocaleString('en-IN', {
                      day: '2-digit',
                      month: '2-digit',
                      year: 'numeric',
                      hour: '2-digit',
                      minute: '2-digit'
                    })}
                  </span>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-xs text-muted-foreground">Version 1.2.3</span>
                <Button
                  variant="ghost"
                  size="sm"
                  iconName="RefreshCw"
                  iconPosition="left"
                  onClick={() => window.location.reload()}
                >
                  Refresh
                </Button>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default SettingsTesting;