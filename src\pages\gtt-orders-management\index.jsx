import React, { useState, useEffect } from 'react';
import { Helmet } from 'react-helmet';
import Icon from '../../components/AppIcon';
import Button from '../../components/ui/Button';
import BuySignalsSection from './components/BuySignalsSection';
import BuyHoldingsSection from './components/BuyHoldingsSection';
import SellOrdersSection from './components/SellOrdersSection';
import ManualOrderForm from './components/ManualOrderForm';

const GTTOrdersManagement = () => {
  const [isManualOrderFormOpen, setIsManualOrderFormOpen] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [lastUpdated, setLastUpdated] = useState(new Date());

  // Mock data for buy signals
  const [buySignals] = useState([
    {
      id: 'BS001',
      symbol: 'RELIANCE',
      exchange: 'NSE',
      triggerPrice: 2456.75,
      suggestedQuantity: 1,
      investment: 2456.75,
      entriesUsed: 0,
      signalDate: '30/07/2025',
      signalTime: '15:30',
      status: 'Active'
    },
    {
      id: 'BS002',
      symbol: 'TCS',
      exchange: 'NSE',
      triggerPrice: 3234.50,
      suggestedQuantity: 1,
      investment: 3234.50,
      entriesUsed: 2,
      signalDate: '29/07/2025',
      signalTime: '14:45',
      status: 'Active'
    },
    {
      id: 'BS003',
      symbol: 'INFY',
      exchange: 'NSE',
      triggerPrice: 1567.25,
      suggestedQuantity: 1,
      investment: 1567.25,
      entriesUsed: 1,
      signalDate: '30/07/2025',
      signalTime: '10:15',
      status: 'Active'
    },
    {
      id: 'BS004',
      symbol: 'HDFCBANK',
      exchange: 'NSE',
      triggerPrice: 1678.90,
      suggestedQuantity: 1,
      investment: 1678.90,
      entriesUsed: 0,
      signalDate: '30/07/2025',
      signalTime: '11:20',
      status: 'Pending'
    }
  ]);

  // Mock data for buy holdings
  const [buyHoldings] = useState([
    {
      id: 'BH001',
      symbol: 'ICICIBANK',
      exchange: 'NSE',
      totalQuantity: 3,
      averagePrice: 1234.67,
      currentPrice: 1289.45,
      targetPrice: 1308.75,
      priceChange: 2.34,
      entries: [
        { quantity: 1, price: 1245.50, amount: 1245.50, date: '25/07/2025' },
        { quantity: 1, price: 1230.25, amount: 1230.25, date: '26/07/2025' },
        { quantity: 1, price: 1228.25, amount: 1228.25, date: '29/07/2025' }
      ]
    },
    {
      id: 'BH002',
      symbol: 'HINDUNILVR',
      exchange: 'NSE',
      totalQuantity: 2,
      averagePrice: 2567.80,
      currentPrice: 2634.25,
      targetPrice: 2721.87,
      priceChange: 1.87,
      entries: [
        { quantity: 1, price: 2578.90, amount: 2578.90, date: '28/07/2025' },
        { quantity: 1, price: 2556.70, amount: 2556.70, date: '29/07/2025' }
      ]
    },
    {
      id: 'BH003',
      symbol: 'ITC',
      exchange: 'NSE',
      totalQuantity: 4,
      averagePrice: 456.25,
      currentPrice: 445.80,
      targetPrice: 483.63,
      priceChange: -1.45,
      entries: [
        { quantity: 2, price: 458.75, amount: 917.50, date: '24/07/2025' },
        { quantity: 1, price: 452.30, amount: 452.30, date: '26/07/2025' },
        { quantity: 1, price: 457.70, amount: 457.70, date: '28/07/2025' }
      ]
    }
  ]);

  // Mock data for sell orders
  const [sellOrders] = useState([
    {
      id: 'SO001',
      symbol: 'SBIN',
      exchange: 'NSE',
      quantity: 2,
      triggerPrice: 634.50,
      currentPrice: 628.75,
      priceChange: -0.85,
      orderType: 'Target',
      status: 'Pending',
      createdDate: '28/07/2025',
      validTill: '27/08/2025'
    },
    {
      id: 'SO002',
      symbol: 'BHARTIARTL',
      exchange: 'NSE',
      quantity: 3,
      triggerPrice: 1245.80,
      currentPrice: 1267.45,
      priceChange: 1.74,
      orderType: 'Target',
      status: 'Triggered',
      createdDate: '26/07/2025',
      validTill: '25/08/2025'
    },
    {
      id: 'SO003',
      symbol: 'KOTAKBANK',
      exchange: 'NSE',
      quantity: 1,
      triggerPrice: 1789.25,
      currentPrice: 1756.90,
      priceChange: -2.15,
      orderType: 'Stop-Loss',
      status: 'Pending',
      createdDate: '29/07/2025',
      validTill: '28/08/2025'
    },
    {
      id: 'SO004',
      symbol: 'WIPRO',
      exchange: 'NSE',
      quantity: 2,
      triggerPrice: 567.30,
      currentPrice: 545.80,
      priceChange: -1.89,
      orderType: 'Target',
      status: 'Expired',
      createdDate: '15/07/2025',
      validTill: '14/08/2025'
    }
  ]);

  useEffect(() => {
    const interval = setInterval(() => {
      setLastUpdated(new Date());
    }, 30000);

    return () => clearInterval(interval);
  }, []);

  const handleRefresh = async () => {
    setRefreshing(true);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1500));
    setLastUpdated(new Date());
    setRefreshing(false);
  };

  const handleCreateOrder = (signalData) => {
    console.log('Creating GTT order for:', signalData);
    // Implementation for creating GTT order
  };

  const handleDeleteSignal = (signalId) => {
    console.log('Deleting signal:', signalId);
    // Implementation for deleting signal
  };

  const handleModifyOrder = (orderData) => {
    console.log('Modifying order:', orderData);
    // Implementation for modifying order
  };

  const handleDeleteOrder = (orderId) => {
    console.log('Deleting order:', orderId);
    // Implementation for deleting order
  };

  const handleCleanupExpired = () => {
    console.log('Cleaning up expired orders');
    // Implementation for cleaning up expired orders
  };

  const handleCreateManualOrder = (orderData) => {
    console.log('Creating manual order:', orderData);
    // Implementation for creating manual order
  };

  const formatTime = (date) => {
    return date.toLocaleTimeString('en-IN', { 
      hour12: false, 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  return (
    <>
      <Helmet>
        <title>GTT Orders Management - Darvas Trading Dashboard</title>
        <meta name="description" content="Monitor and control Good Till Triggered orders generated by automated Darvas Box strategy with real-time price updates and order management." />
      </Helmet>

      <div className="min-h-screen bg-background pt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Page Header */}
          <div className="mb-8">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center">
                  <Icon name="GitBranch" size={24} color="var(--color-primary)" />
                </div>
                <div>
                  <h1 className="text-2xl font-semibold text-foreground">GTT Orders Management</h1>
                  <p className="text-muted-foreground">
                    Monitor and control Good Till Triggered orders from Darvas Box strategy
                  </p>
                </div>
              </div>
              
              <div className="flex items-center space-x-3">
                <div className="hidden sm:flex items-center space-x-2 text-sm text-muted-foreground">
                  <Icon name="Clock" size={16} />
                  <span>Last updated: {formatTime(lastUpdated)}</span>
                </div>
                
                <Button
                  variant="outline"
                  onClick={handleRefresh}
                  loading={refreshing}
                  iconName="RefreshCw"
                  iconPosition="left"
                >
                  Refresh
                </Button>
                
                <Button
                  onClick={() => setIsManualOrderFormOpen(true)}
                  iconName="Plus"
                  iconPosition="left"
                >
                  Manual Order
                </Button>
              </div>
            </div>

            {/* Quick Stats */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="bg-card rounded-lg border border-border p-4">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-success/10 rounded-lg flex items-center justify-center">
                    <Icon name="TrendingUp" size={16} color="var(--color-success)" />
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Buy Signals</p>
                    <p className="text-lg font-semibold text-foreground">{buySignals.length}</p>
                  </div>
                </div>
              </div>
              
              <div className="bg-card rounded-lg border border-border p-4">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-accent/10 rounded-lg flex items-center justify-center">
                    <Icon name="Package" size={16} color="var(--color-accent)" />
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Holdings</p>
                    <p className="text-lg font-semibold text-foreground">{buyHoldings.length}</p>
                  </div>
                </div>
              </div>
              
              <div className="bg-card rounded-lg border border-border p-4">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-warning/10 rounded-lg flex items-center justify-center">
                    <Icon name="TrendingDown" size={16} color="var(--color-warning)" />
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Sell Orders</p>
                    <p className="text-lg font-semibold text-foreground">
                      {sellOrders.filter(order => order.status === 'Pending').length}
                    </p>
                  </div>
                </div>
              </div>
              
              <div className="bg-card rounded-lg border border-border p-4">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-error/10 rounded-lg flex items-center justify-center">
                    <Icon name="AlertTriangle" size={16} color="var(--color-error)" />
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Expired</p>
                    <p className="text-lg font-semibold text-foreground">
                      {sellOrders.filter(order => order.status === 'Expired').length}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Main Content Sections */}
          <div className="space-y-8">
            {/* Buy Signals Section */}
            <BuySignalsSection
              buySignals={buySignals}
              onCreateOrder={handleCreateOrder}
              onDeleteSignal={handleDeleteSignal}
            />

            {/* Buy Holdings Section */}
            <BuyHoldingsSection
              buyHoldings={buyHoldings}
              onModifyOrder={handleModifyOrder}
              onDeleteOrder={handleDeleteOrder}
            />

            {/* Sell Orders Section */}
            <SellOrdersSection
              sellOrders={sellOrders}
              onModifyOrder={handleModifyOrder}
              onDeleteOrder={handleDeleteOrder}
              onCleanupExpired={handleCleanupExpired}
            />
          </div>
        </div>

        {/* Manual Order Form Modal */}
        <ManualOrderForm
          isOpen={isManualOrderFormOpen}
          onClose={() => setIsManualOrderFormOpen(false)}
          onCreateOrder={handleCreateManualOrder}
        />
      </div>
    </>
  );
};

export default GTTOrdersManagement;