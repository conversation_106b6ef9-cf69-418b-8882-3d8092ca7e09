import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';
import Input from '../../../components/ui/Input';
import { Checkbox } from '../../../components/ui/Checkbox';

const StrategySettings = () => {
  const [settings, setSettings] = useState({
    signalGenerationEnabled: true,
    fridaySignalTime: '20:00',
    maxCapitalPerStock: 10000,
    maxTradesPerStock: 5,
    maxTradeAmount: 2000,
    targetProfitPercentage: 6,
    autoResumeEnabled: true,
    weekendProcessing: false,
    emergencyStopEnabled: true
  });

  const [apiSettings, setApiSettings] = useState({
    yahooFinanceEnabled: true,
    apiTimeout: 30,
    retryAttempts: 3,
    rateLimitPerMinute: 100,
    cacheEnabled: true,
    cacheDuration: 300
  });

  const [connectionStatus, setConnectionStatus] = useState({
    yahooFinance: 'connected',
    database: 'connected',
    lastSync: new Date(Date.now() - 120000)
  });

  const handleSettingChange = (key, value) => {
    setSettings(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleApiSettingChange = (key, value) => {
    setApiSettings(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const saveSettings = () => {
    console.log('Saving settings:', { settings, apiSettings });
    // Mock save operation
  };

  const resetToDefaults = () => {
    setSettings({
      signalGenerationEnabled: true,
      fridaySignalTime: '20:00',
      maxCapitalPerStock: 10000,
      maxTradesPerStock: 5,
      maxTradeAmount: 2000,
      targetProfitPercentage: 6,
      autoResumeEnabled: true,
      weekendProcessing: false,
      emergencyStopEnabled: true
    });
    setApiSettings({
      yahooFinanceEnabled: true,
      apiTimeout: 30,
      retryAttempts: 3,
      rateLimitPerMinute: 100,
      cacheEnabled: true,
      cacheDuration: 300
    });
  };

  const testConnection = (service) => {
    console.log(`Testing ${service} connection...`);
    setConnectionStatus(prev => ({
      ...prev,
      [service]: 'testing'
    }));
    
    setTimeout(() => {
      setConnectionStatus(prev => ({
        ...prev,
        [service]: 'connected',
        lastSync: new Date()
      }));
    }, 2000);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'connected': return 'text-success';
      case 'disconnected': return 'text-error';
      case 'testing': return 'text-warning';
      default: return 'text-muted-foreground';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'connected': return 'CheckCircle';
      case 'disconnected': return 'XCircle';
      case 'testing': return 'Loader';
      default: return 'AlertCircle';
    }
  };

  return (
    <div className="space-y-8">
      {/* Strategy Configuration */}
      <div className="bg-card rounded-lg border border-border p-6">
        <div className="flex items-center space-x-3 mb-6">
          <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
            <Icon name="Settings" size={20} color="var(--color-primary)" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-foreground">Strategy Configuration</h3>
            <p className="text-sm text-muted-foreground">Configure automated trading parameters</p>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="space-y-4">
            <Checkbox
              label="Enable Automated Signal Generation"
              description="Automatically generate buy/sell signals based on Darvas Box strategy"
              checked={settings.signalGenerationEnabled}
              onChange={(e) => handleSettingChange('signalGenerationEnabled', e.target.checked)}
            />

            <Input
              label="Friday Signal Generation Time"
              type="time"
              description="Time when weekly signals are generated (IST)"
              value={settings.fridaySignalTime}
              onChange={(e) => handleSettingChange('fridaySignalTime', e.target.value)}
            />

            <Input
              label="Maximum Capital Per Stock (₹)"
              type="number"
              description="Maximum amount to invest in a single stock"
              value={settings.maxCapitalPerStock}
              onChange={(e) => handleSettingChange('maxCapitalPerStock', parseInt(e.target.value))}
              min="1000"
              max="100000"
            />

            <Input
              label="Maximum Trades Per Stock"
              type="number"
              description="Maximum number of entries allowed per stock"
              value={settings.maxTradesPerStock}
              onChange={(e) => handleSettingChange('maxTradesPerStock', parseInt(e.target.value))}
              min="1"
              max="10"
            />
          </div>

          <div className="space-y-4">
            <Input
              label="Maximum Trade Amount (₹)"
              type="number"
              description="Maximum amount per individual trade"
              value={settings.maxTradeAmount}
              onChange={(e) => handleSettingChange('maxTradeAmount', parseInt(e.target.value))}
              min="500"
              max="50000"
            />

            <Input
              label="Target Profit Percentage (%)"
              type="number"
              description="Target profit percentage for sell orders"
              value={settings.targetProfitPercentage}
              onChange={(e) => handleSettingChange('targetProfitPercentage', parseFloat(e.target.value))}
              min="1"
              max="20"
              step="0.1"
            />

            <Checkbox
              label="Auto Resume Strategy"
              description="Automatically resume strategy when capital becomes available"
              checked={settings.autoResumeEnabled}
              onChange={(e) => handleSettingChange('autoResumeEnabled', e.target.checked)}
            />

            <Checkbox
              label="Weekend Processing"
              description="Process signals and orders during weekends"
              checked={settings.weekendProcessing}
              onChange={(e) => handleSettingChange('weekendProcessing', e.target.checked)}
            />
          </div>
        </div>
      </div>

      {/* API Configuration */}
      <div className="bg-card rounded-lg border border-border p-6">
        <div className="flex items-center space-x-3 mb-6">
          <div className="w-10 h-10 bg-accent/10 rounded-lg flex items-center justify-center">
            <Icon name="Globe" size={20} color="var(--color-accent)" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-foreground">API Configuration</h3>
            <p className="text-sm text-muted-foreground">Configure external data sources and connections</p>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="space-y-4">
            <Checkbox
              label="Yahoo Finance API"
              description="Enable Yahoo Finance for market data"
              checked={apiSettings.yahooFinanceEnabled}
              onChange={(e) => handleApiSettingChange('yahooFinanceEnabled', e.target.checked)}
            />

            <Input
              label="API Timeout (seconds)"
              type="number"
              description="Request timeout for API calls"
              value={apiSettings.apiTimeout}
              onChange={(e) => handleApiSettingChange('apiTimeout', parseInt(e.target.value))}
              min="5"
              max="120"
            />

            <Input
              label="Retry Attempts"
              type="number"
              description="Number of retry attempts for failed requests"
              value={apiSettings.retryAttempts}
              onChange={(e) => handleApiSettingChange('retryAttempts', parseInt(e.target.value))}
              min="1"
              max="10"
            />
          </div>

          <div className="space-y-4">
            <Input
              label="Rate Limit (per minute)"
              type="number"
              description="Maximum API calls per minute"
              value={apiSettings.rateLimitPerMinute}
              onChange={(e) => handleApiSettingChange('rateLimitPerMinute', parseInt(e.target.value))}
              min="10"
              max="1000"
            />

            <Checkbox
              label="Enable Caching"
              description="Cache API responses to reduce calls"
              checked={apiSettings.cacheEnabled}
              onChange={(e) => handleApiSettingChange('cacheEnabled', e.target.checked)}
            />

            <Input
              label="Cache Duration (seconds)"
              type="number"
              description="How long to cache API responses"
              value={apiSettings.cacheDuration}
              onChange={(e) => handleApiSettingChange('cacheDuration', parseInt(e.target.value))}
              min="60"
              max="3600"
              disabled={!apiSettings.cacheEnabled}
            />
          </div>
        </div>
      </div>

      {/* Connection Status */}
      <div className="bg-card rounded-lg border border-border p-6">
        <div className="flex items-center space-x-3 mb-6">
          <div className="w-10 h-10 bg-success/10 rounded-lg flex items-center justify-center">
            <Icon name="Wifi" size={20} color="var(--color-success)" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-foreground">Connection Status</h3>
            <p className="text-sm text-muted-foreground">Monitor external service connections</p>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div className="bg-muted/50 rounded-lg p-4">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium">Yahoo Finance</span>
              <div className="flex items-center space-x-2">
                <Icon 
                  name={getStatusIcon(connectionStatus.yahooFinance)} 
                  size={16} 
                  className={`${getStatusColor(connectionStatus.yahooFinance)} ${
                    connectionStatus.yahooFinance === 'testing' ? 'animate-spin' : ''
                  }`}
                />
                <span className={`text-xs font-medium capitalize ${getStatusColor(connectionStatus.yahooFinance)}`}>
                  {connectionStatus.yahooFinance}
                </span>
              </div>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => testConnection('yahooFinance')}
              disabled={connectionStatus.yahooFinance === 'testing'}
              className="w-full"
            >
              Test Connection
            </Button>
          </div>

          <div className="bg-muted/50 rounded-lg p-4">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium">Database</span>
              <div className="flex items-center space-x-2">
                <Icon 
                  name={getStatusIcon(connectionStatus.database)} 
                  size={16} 
                  className={`${getStatusColor(connectionStatus.database)} ${
                    connectionStatus.database === 'testing' ? 'animate-spin' : ''
                  }`}
                />
                <span className={`text-xs font-medium capitalize ${getStatusColor(connectionStatus.database)}`}>
                  {connectionStatus.database}
                </span>
              </div>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => testConnection('database')}
              disabled={connectionStatus.database === 'testing'}
              className="w-full"
            >
              Test Connection
            </Button>
          </div>

          <div className="bg-muted/50 rounded-lg p-4 md:col-span-2 lg:col-span-1">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium">Last Sync</span>
              <Icon name="Clock" size={16} className="text-muted-foreground" />
            </div>
            <div className="text-xs text-muted-foreground">
              {connectionStatus.lastSync.toLocaleString('en-IN', {
                day: '2-digit',
                month: '2-digit',
                year: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
              })}
            </div>
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex flex-col sm:flex-row gap-4">
        <Button
          variant="default"
          onClick={saveSettings}
          iconName="Save"
          iconPosition="left"
          className="flex-1 sm:flex-none"
        >
          Save Settings
        </Button>
        <Button
          variant="outline"
          onClick={resetToDefaults}
          iconName="RotateCcw"
          iconPosition="left"
          className="flex-1 sm:flex-none"
        >
          Reset to Defaults
        </Button>
      </div>
    </div>
  );
};

export default StrategySettings;