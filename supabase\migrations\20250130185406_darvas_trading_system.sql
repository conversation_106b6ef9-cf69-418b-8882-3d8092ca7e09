-- Location: supabase/migrations/20250130185406_darvas_trading_system.sql
-- Schema Analysis: New Darvas Box Trading Strategy System
-- Integration Type: Complete new trading system implementation
-- Dependencies: Fresh schema for trading strategy application

-- 1. Custom Types
CREATE TYPE public.user_role AS ENUM ('admin', 'trader', 'viewer');
CREATE TYPE public.strategy_status AS ENUM ('active', 'paused', 'stopped');
CREATE TYPE public.order_type AS ENUM ('buy_signal', 'buy_holding', 'sell_target');
CREATE TYPE public.order_status AS ENUM ('pending', 'triggered', 'cancelled', 'failed');
CREATE TYPE public.trade_type AS ENUM ('buy', 'sell');
CREATE TYPE public.trade_status AS ENUM ('completed', 'partial', 'failed');

-- 2. Core Tables
-- User profiles for auth intermediary
CREATE TABLE public.user_profiles (
    id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    email TEXT NOT NULL UNIQUE,
    full_name TEXT NOT NULL,
    role public.user_role DEFAULT 'trader'::public.user_role,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- Capital management
CREATE TABLE public.capital_tracker (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
    total_capital DECIMAL(12,2) NOT NULL DEFAULT 0,
    used_capital DECIMAL(12,2) NOT NULL DEFAULT 0,
    available_capital DECIMAL(12,2) NOT NULL DEFAULT 0,
    per_stock_limit DECIMAL(12,2) NOT NULL DEFAULT 10000,
    per_trade_limit DECIMAL(12,2) NOT NULL DEFAULT 2000,
    max_entries_per_stock INTEGER NOT NULL DEFAULT 5,
    strategy_status public.strategy_status DEFAULT 'paused'::public.strategy_status,
    last_updated TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- BOH eligible stocks scanner
CREATE TABLE public.boh_eligible_stocks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    symbol TEXT NOT NULL,
    name TEXT NOT NULL,
    exchange TEXT DEFAULT 'NSE',
    current_price DECIMAL(10,2),
    week_high_52 DECIMAL(10,2),
    week_low_52 DECIMAL(10,2),
    week_high_date DATE,
    week_low_date DATE,
    is_boh_eligible BOOLEAN DEFAULT false,
    last_scanned TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(symbol, exchange)
);

-- Weekly signals generator
CREATE TABLE public.weekly_signals (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    symbol TEXT NOT NULL,
    exchange TEXT DEFAULT 'NSE',
    week_start DATE NOT NULL,
    week_end DATE NOT NULL,
    week_high DECIMAL(10,2) NOT NULL,
    week_low DECIMAL(10,2) NOT NULL,
    week_open DECIMAL(10,2) NOT NULL,
    week_close DECIMAL(10,2) NOT NULL,
    suggested_buy_price DECIMAL(10,2) NOT NULL,
    suggested_quantity INTEGER NOT NULL,
    current_price DECIMAL(10,2),
    price_difference_percent DECIMAL(5,2),
    signal_generated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    is_processed BOOLEAN DEFAULT false
);

-- GTT Orders management
CREATE TABLE public.gtt_orders (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
    symbol TEXT NOT NULL,
    exchange TEXT DEFAULT 'NSE',
    order_type public.order_type NOT NULL,
    trigger_price DECIMAL(10,2) NOT NULL,
    quantity INTEGER NOT NULL,
    current_price DECIMAL(10,2),
    status public.order_status DEFAULT 'pending'::public.order_status,
    condition_text TEXT,
    external_order_id TEXT,
    symbol_token TEXT,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    triggered_at TIMESTAMPTZ,
    cancelled_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- Current holdings tracker
CREATE TABLE public.current_holdings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
    symbol TEXT NOT NULL,
    name TEXT NOT NULL,
    exchange TEXT DEFAULT 'NSE',
    total_quantity INTEGER NOT NULL DEFAULT 0,
    total_invested DECIMAL(12,2) NOT NULL DEFAULT 0,
    average_price DECIMAL(10,2) NOT NULL DEFAULT 0,
    current_price DECIMAL(10,2),
    target_price DECIMAL(10,2),
    last_week_high DECIMAL(10,2),
    ignorable_lower_price DECIMAL(10,2),
    is_outside_ignorable_range BOOLEAN DEFAULT false,
    entry_count INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, symbol, exchange)
);

-- Trade log for all transactions
CREATE TABLE public.trade_log (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
    symbol TEXT NOT NULL,
    exchange TEXT DEFAULT 'NSE',
    trade_type public.trade_type NOT NULL,
    quantity INTEGER NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    amount DECIMAL(12,2) NOT NULL,
    entry_number INTEGER,
    trade_date TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    strategy_name TEXT DEFAULT 'Darvas Box',
    target_price DECIMAL(10,2),
    sell_price DECIMAL(10,2),
    sell_date TIMESTAMPTZ,
    profit_amount DECIMAL(12,2),
    profit_percentage DECIMAL(5,2),
    holding_days INTEGER,
    capital_used DECIMAL(12,2),
    status public.trade_status DEFAULT 'completed'::public.trade_status,
    gtt_order_id UUID REFERENCES public.gtt_orders(id),
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- OHLC historical data for weekly analysis
CREATE TABLE public.ohlc_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    symbol TEXT NOT NULL,
    exchange TEXT DEFAULT 'NSE',
    trade_date DATE NOT NULL,
    open_price DECIMAL(10,2) NOT NULL,
    high_price DECIMAL(10,2) NOT NULL,
    low_price DECIMAL(10,2) NOT NULL,
    close_price DECIMAL(10,2) NOT NULL,
    volume BIGINT,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(symbol, exchange, trade_date)
);

-- 3. Essential Indexes
CREATE INDEX idx_user_profiles_email ON public.user_profiles(email);
CREATE INDEX idx_capital_tracker_user_id ON public.capital_tracker(user_id);
CREATE INDEX idx_boh_eligible_symbol ON public.boh_eligible_stocks(symbol);
CREATE INDEX idx_boh_eligible_scan_date ON public.boh_eligible_stocks(last_scanned);
CREATE INDEX idx_weekly_signals_symbol ON public.weekly_signals(symbol);
CREATE INDEX idx_weekly_signals_date ON public.weekly_signals(signal_generated_at);
CREATE INDEX idx_gtt_orders_user_id ON public.gtt_orders(user_id);
CREATE INDEX idx_gtt_orders_symbol ON public.gtt_orders(symbol);
CREATE INDEX idx_gtt_orders_status ON public.gtt_orders(status);
CREATE INDEX idx_holdings_user_id ON public.current_holdings(user_id);
CREATE INDEX idx_holdings_symbol ON public.current_holdings(symbol);
CREATE INDEX idx_trade_log_user_id ON public.trade_log(user_id);
CREATE INDEX idx_trade_log_symbol ON public.trade_log(symbol);
CREATE INDEX idx_trade_log_date ON public.trade_log(trade_date);
CREATE INDEX idx_ohlc_symbol_date ON public.ohlc_history(symbol, trade_date);

-- 4. Enable RLS
ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.capital_tracker ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.boh_eligible_stocks ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.weekly_signals ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.gtt_orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.current_holdings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.trade_log ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.ohlc_history ENABLE ROW LEVEL SECURITY;

-- 5. Helper Functions for RLS
CREATE OR REPLACE FUNCTION public.is_owner(user_uuid UUID)
RETURNS BOOLEAN
LANGUAGE sql
STABLE
SECURITY DEFINER
AS $$
SELECT EXISTS (
    SELECT 1 FROM public.user_profiles up
    WHERE up.id = user_uuid AND up.id = auth.uid()
)
$$;

CREATE OR REPLACE FUNCTION public.has_role(required_role TEXT)
RETURNS BOOLEAN
LANGUAGE sql
STABLE
SECURITY DEFINER
AS $$
SELECT EXISTS (
    SELECT 1 FROM public.user_profiles up
    WHERE up.id = auth.uid() AND up.role::TEXT = required_role
)
$$;

CREATE OR REPLACE FUNCTION public.can_access_user_data(data_user_id UUID)
RETURNS BOOLEAN
LANGUAGE sql
STABLE
SECURITY DEFINER
AS $$
SELECT data_user_id = auth.uid() OR public.has_role('admin')
$$;

-- 6. RLS Policies
-- User profiles
CREATE POLICY "users_own_profile" ON public.user_profiles FOR ALL
USING (public.is_owner(id)) WITH CHECK (public.is_owner(id));

-- Capital tracker
CREATE POLICY "users_own_capital_data" ON public.capital_tracker FOR ALL
USING (public.can_access_user_data(user_id)) WITH CHECK (public.can_access_user_data(user_id));

-- BOH eligible stocks (public read, admin write)
CREATE POLICY "public_read_boh_stocks" ON public.boh_eligible_stocks FOR SELECT
TO public USING (true);

CREATE POLICY "admin_manage_boh_stocks" ON public.boh_eligible_stocks FOR ALL
TO authenticated USING (public.has_role('admin')) WITH CHECK (public.has_role('admin'));

-- Weekly signals (public read, system write)
CREATE POLICY "public_read_signals" ON public.weekly_signals FOR SELECT
TO public USING (true);

CREATE POLICY "admin_manage_signals" ON public.weekly_signals FOR ALL
TO authenticated USING (public.has_role('admin')) WITH CHECK (public.has_role('admin'));

-- GTT orders (user owns)
CREATE POLICY "users_own_gtt_orders" ON public.gtt_orders FOR ALL
USING (public.can_access_user_data(user_id)) WITH CHECK (public.can_access_user_data(user_id));

-- Current holdings (user owns)
CREATE POLICY "users_own_holdings" ON public.current_holdings FOR ALL
USING (public.can_access_user_data(user_id)) WITH CHECK (public.can_access_user_data(user_id));

-- Trade log (user owns)
CREATE POLICY "users_own_trades" ON public.trade_log FOR ALL
USING (public.can_access_user_data(user_id)) WITH CHECK (public.can_access_user_data(user_id));

-- OHLC history (public read, admin write)
CREATE POLICY "public_read_ohlc" ON public.ohlc_history FOR SELECT
TO public USING (true);

CREATE POLICY "admin_manage_ohlc" ON public.ohlc_history FOR ALL
TO authenticated USING (public.has_role('admin')) WITH CHECK (public.has_role('admin'));

-- 7. Functions for automatic profile creation
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER
SECURITY DEFINER
LANGUAGE plpgsql
AS $$
BEGIN
  INSERT INTO public.user_profiles (id, email, full_name, role)
  VALUES (
    NEW.id, 
    NEW.email, 
    COALESCE(NEW.raw_user_meta_data->>'full_name', split_part(NEW.email, '@', 1)),
    COALESCE(NEW.raw_user_meta_data->>'role', 'trader')::public.user_role
  );
  
  -- Initialize capital tracker for new user
  INSERT INTO public.capital_tracker (user_id, total_capital, used_capital, available_capital)
  VALUES (NEW.id, 0, 0, 0);
  
  RETURN NEW;
END;
$$;

-- Trigger for new user creation
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- 8. Business Logic Functions
-- Calculate holdings summary
CREATE OR REPLACE FUNCTION public.update_holding_summary(holding_user_id UUID, holding_symbol TEXT)
RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    total_qty INTEGER := 0;
    total_inv DECIMAL(12,2) := 0;
    avg_price DECIMAL(10,2) := 0;
    entry_cnt INTEGER := 0;
BEGIN
    -- Calculate totals from trade log
    SELECT 
        COALESCE(SUM(CASE WHEN trade_type = 'buy' THEN quantity ELSE -quantity END), 0),
        COALESCE(SUM(CASE WHEN trade_type = 'buy' THEN amount ELSE -amount END), 0),
        COUNT(CASE WHEN trade_type = 'buy' THEN 1 END)
    INTO total_qty, total_inv, entry_cnt
    FROM public.trade_log
    WHERE user_id = holding_user_id AND symbol = holding_symbol;
    
    -- Calculate average price
    IF total_qty > 0 THEN
        avg_price := total_inv / total_qty;
    END IF;
    
    -- Update or insert holding record
    INSERT INTO public.current_holdings (
        user_id, symbol, name, total_quantity, total_invested, 
        average_price, entry_count, updated_at
    ) VALUES (
        holding_user_id, holding_symbol, holding_symbol, 
        total_qty, total_inv, avg_price, entry_cnt, CURRENT_TIMESTAMP
    )
    ON CONFLICT (user_id, symbol, exchange) 
    DO UPDATE SET
        total_quantity = total_qty,
        total_invested = total_inv,
        average_price = avg_price,
        entry_count = entry_cnt,
        updated_at = CURRENT_TIMESTAMP;
END;
$$;

-- Update capital usage
CREATE OR REPLACE FUNCTION public.update_capital_usage(capital_user_id UUID)
RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    used_amount DECIMAL(12,2) := 0;
    total_amount DECIMAL(12,2) := 0;
BEGIN
    -- Calculate used capital from current holdings
    SELECT COALESCE(SUM(total_invested), 0)
    INTO used_amount
    FROM public.current_holdings
    WHERE user_id = capital_user_id AND total_quantity > 0;
    
    -- Get total capital
    SELECT total_capital INTO total_amount
    FROM public.capital_tracker
    WHERE user_id = capital_user_id;
    
    -- Update capital tracker
    UPDATE public.capital_tracker
    SET 
        used_capital = used_amount,
        available_capital = total_amount - used_amount,
        last_updated = CURRENT_TIMESTAMP
    WHERE user_id = capital_user_id;
END;
$$;

-- 9. Sample Data for Testing
DO $$
DECLARE
    admin_uuid UUID := gen_random_uuid();
    trader_uuid UUID := gen_random_uuid();
    project_uuid UUID := gen_random_uuid();
    signal_uuid UUID := gen_random_uuid();
BEGIN
    -- Create auth users with required fields
    INSERT INTO auth.users (
        id, instance_id, aud, role, email, encrypted_password, email_confirmed_at,
        created_at, updated_at, raw_user_meta_data, raw_app_meta_data,
        is_sso_user, is_anonymous, confirmation_token, confirmation_sent_at,
        recovery_token, recovery_sent_at, email_change_token_new, email_change,
        email_change_sent_at, email_change_token_current, email_change_confirm_status,
        reauthentication_token, reauthentication_sent_at, phone, phone_change,
        phone_change_token, phone_change_sent_at
    ) VALUES
        (admin_uuid, '00000000-0000-0000-0000-000000000000', 'authenticated', 'authenticated',
         '<EMAIL>', crypt('admin123', gen_salt('bf', 10)), now(), now(), now(),
         '{"full_name": "Trading Admin", "role": "admin"}'::jsonb, '{"provider": "email", "providers": ["email"]}'::jsonb,
         false, false, '', null, '', null, '', '', null, '', 0, '', null, null, '', '', null),
        (trader_uuid, '00000000-0000-0000-0000-000000000000', 'authenticated', 'authenticated',
         '<EMAIL>', crypt('trader123', gen_salt('bf', 10)), now(), now(), now(),
         '{"full_name": "Demo Trader", "role": "trader"}'::jsonb, '{"provider": "email", "providers": ["email"]}'::jsonb,
         false, false, '', null, '', null, '', '', null, '', 0, '', null, null, '', '', null);

    -- Sample BOH eligible stocks
    INSERT INTO public.boh_eligible_stocks (symbol, name, current_price, week_high_52, week_low_52, week_high_date, week_low_date, is_boh_eligible) VALUES
        ('RELIANCE', 'Reliance Industries Ltd', 2589.30, 2968.00, 2220.00, '2024-09-15', '2024-12-20', true),
        ('TCS', 'Tata Consultancy Services', 3189.75, 4592.25, 3100.00, '2024-07-10', '2024-11-30', true),
        ('INFY', 'Infosys Limited', 1623.40, 1953.90, 1350.00, '2024-06-25', '2024-10-15', true),
        ('HDFC', 'HDFC Bank Limited', 1598.25, 1725.00, 1475.00, '2024-08-05', '2024-12-01', false),
        ('ICICIBANK', 'ICICI Bank Limited', 1189.90, 1297.00, 980.00, '2024-05-20', '2024-11-10', true);

    -- Sample weekly signals
    INSERT INTO public.weekly_signals (symbol, week_start, week_end, week_high, week_low, week_open, week_close, suggested_buy_price, suggested_quantity, current_price, price_difference_percent) VALUES
        ('RELIANCE', '2025-01-20', '2025-01-24', 2595.00, 2540.00, 2560.00, 2589.30, 2600.05, 7, 2589.30, 0.42),
        ('TCS', '2025-01-20', '2025-01-24', 3210.00, 3180.00, 3195.00, 3189.75, 3215.05, 6, 3189.75, 0.79),
        ('INFY', '2025-01-20', '2025-01-24', 1635.00, 1610.00, 1620.00, 1623.40, 1640.05, 12, 1623.40, 1.03);

    -- Sample capital settings for trader
    UPDATE public.capital_tracker 
    SET 
        total_capital = 500000,
        used_capital = 234500,
        available_capital = 265500,
        strategy_status = 'active'::public.strategy_status
    WHERE user_id = trader_uuid;

    -- Sample GTT orders
    INSERT INTO public.gtt_orders (user_id, symbol, order_type, trigger_price, quantity, current_price, condition_text) VALUES
        (trader_uuid, 'HDFC', 'buy_signal'::public.order_type, 1645.50, 12, 1598.25, 'Trigger when price >= ₹1,645.50'),
        (trader_uuid, 'ICICIBANK', 'buy_signal'::public.order_type, 1234.75, 16, 1189.90, 'Trigger when price >= ₹1,234.75'),
        (trader_uuid, 'SBIN', 'buy_signal'::public.order_type, 789.25, 25, 756.80, 'Trigger when price >= ₹789.25');

    -- Sample current holdings
    INSERT INTO public.current_holdings (user_id, symbol, name, total_quantity, total_invested, average_price, current_price, target_price, entry_count) VALUES
        (trader_uuid, 'RELIANCE', 'Reliance Industries Ltd', 25, 61419, 2456.75, 2589.30, 2604.16, 3),
        (trader_uuid, 'TCS', 'Tata Consultancy Services', 15, 48518, 3234.50, 3189.75, 3428.57, 2),
        (trader_uuid, 'INFY', 'Infosys Limited', 35, 54854, 1567.25, 1623.40, 1661.29, 3);

    -- Sample trade log entries
    INSERT INTO public.trade_log (user_id, symbol, trade_type, quantity, price, amount, entry_number, strategy_name, capital_used) VALUES
        (trader_uuid, 'RELIANCE', 'buy'::public.trade_type, 8, 2445.50, 19564, 1, 'Darvas Box', 19564),
        (trader_uuid, 'RELIANCE', 'buy'::public.trade_type, 10, 2467.20, 24672, 2, 'Darvas Box', 24672),
        (trader_uuid, 'RELIANCE', 'buy'::public.trade_type, 7, 2458.90, 17212, 3, 'Darvas Box', 17212),
        (trader_uuid, 'TCS', 'buy'::public.trade_type, 6, 3245.80, 19475, 1, 'Darvas Box', 19475),
        (trader_uuid, 'TCS', 'buy'::public.trade_type, 9, 3227.30, 29046, 2, 'Darvas Box', 29046),
        (trader_uuid, 'INFY', 'buy'::public.trade_type, 12, 1556.75, 18681, 1, 'Darvas Box', 18681),
        (trader_uuid, 'INFY', 'buy'::public.trade_type, 13, 1572.90, 20448, 2, 'Darvas Box', 20448),
        (trader_uuid, 'INFY', 'buy'::public.trade_type, 10, 1571.20, 15712, 3, 'Darvas Box', 15712);

    -- Sample OHLC data for the week
    INSERT INTO public.ohlc_history (symbol, trade_date, open_price, high_price, low_price, close_price, volume) VALUES
        ('RELIANCE', '2025-01-20', 2560.00, 2595.00, 2540.00, 2578.50, 1250000),
        ('RELIANCE', '2025-01-21', 2580.00, 2590.00, 2565.00, 2582.75, 1180000),
        ('RELIANCE', '2025-01-22', 2585.00, 2592.00, 2570.00, 2587.25, 1090000),
        ('RELIANCE', '2025-01-23', 2588.00, 2595.00, 2575.00, 2589.30, 1300000),
        ('RELIANCE', '2025-01-24', 2590.00, 2595.00, 2580.00, 2589.30, 980000);

EXCEPTION
    WHEN foreign_key_violation THEN
        RAISE NOTICE 'Foreign key error: %', SQLERRM;
    WHEN unique_violation THEN
        RAISE NOTICE 'Unique constraint error: %', SQLERRM;
    WHEN OTHERS THEN
        RAISE NOTICE 'Unexpected error: %', SQLERRM;
END $$;