import React from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';
import Input from '../../../components/ui/Input';

const FilterControls = ({ 
  filters, 
  onFilterChange, 
  onClearFilters, 
  onExport,
  totalStocks,
  eligibleStocks 
}) => {
  const marketCapOptions = [
    { value: 'all', label: 'All Market Cap' },
    { value: 'large', label: 'Large Cap (₹20,000+ Cr)' },
    { value: 'mid', label: 'Mid Cap (₹5,000-20,000 Cr)' },
    { value: 'small', label: 'Small Cap (&lt;₹5,000 Cr)' }
  ];

  const sectorOptions = [
    { value: 'all', label: 'All Sectors' },
    { value: 'banking', label: 'Banking' },
    { value: 'it', label: 'Information Technology' },
    { value: 'pharma', label: 'Pharmaceuticals' },
    { value: 'auto', label: 'Automobile' },
    { value: 'fmcg', label: 'FMCG' },
    { value: 'metals', label: 'Metals' },
    { value: 'energy', label: 'Energy' },
    { value: 'realty', label: 'Real Estate' }
  ];

  const proximityOptions = [
    { value: 'all', label: 'All Stocks' },
    { value: 'eligible', label: 'Eligible Only (At 52W High)' },
    { value: 'near', label: 'Near Eligible (Within 5%)' },
    { value: 'potential', label: 'Potential (Within 10%)' }
  ];

  const handleInputChange = (field, value) => {
    onFilterChange({ ...filters, [field]: value });
  };

  const hasActiveFilters = () => {
    return filters.search || 
           filters.marketCap !== 'all' || 
           filters.sector !== 'all' || 
           filters.proximity !== 'all' || 
           filters.minPrice || 
           filters.maxPrice;
  };

  return (
    <div className="bg-card rounded-lg border border-border p-6 mb-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-lg font-semibold text-foreground">Filter Stocks</h2>
          <p className="text-sm text-muted-foreground mt-1">
            {totalStocks} total stocks • {eligibleStocks} eligible for BOH strategy
          </p>
        </div>
        <div className="flex items-center space-x-3">
          {hasActiveFilters() && (
            <Button
              variant="outline"
              size="sm"
              onClick={onClearFilters}
            >
              <Icon name="X" size={14} />
              Clear Filters
            </Button>
          )}
          <Button
            variant="outline"
            size="sm"
            onClick={onExport}
          >
            <Icon name="Download" size={14} />
            Export
          </Button>
        </div>
      </div>

      {/* Filter Controls */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
        {/* Search */}
        <div className="lg:col-span-1">
          <Input
            type="search"
            placeholder="Search by symbol or name..."
            value={filters.search}
            onChange={(e) => handleInputChange('search', e.target.value)}
            className="w-full"
          />
        </div>

        {/* Market Cap Filter */}
        <div>
          <select
            value={filters.marketCap}
            onChange={(e) => handleInputChange('marketCap', e.target.value)}
            className="w-full px-3 py-2 text-sm bg-background border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
          >
            {marketCapOptions.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>

        {/* Sector Filter */}
        <div>
          <select
            value={filters.sector}
            onChange={(e) => handleInputChange('sector', e.target.value)}
            className="w-full px-3 py-2 text-sm bg-background border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
          >
            {sectorOptions.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>

        {/* Proximity Filter */}
        <div>
          <select
            value={filters.proximity}
            onChange={(e) => handleInputChange('proximity', e.target.value)}
            className="w-full px-3 py-2 text-sm bg-background border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
          >
            {proximityOptions.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Price Range */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <Input
            type="number"
            label="Minimum Price"
            placeholder="₹0"
            value={filters.minPrice}
            onChange={(e) => handleInputChange('minPrice', e.target.value)}
            min="0"
            step="0.01"
          />
        </div>
        <div>
          <Input
            type="number"
            label="Maximum Price"
            placeholder="₹10,000"
            value={filters.maxPrice}
            onChange={(e) => handleInputChange('maxPrice', e.target.value)}
            min="0"
            step="0.01"
          />
        </div>
      </div>

      {/* Active Filters Summary */}
      {hasActiveFilters() && (
        <div className="mt-4 pt-4 border-t border-border">
          <div className="flex items-center space-x-2 text-sm text-muted-foreground">
            <Icon name="Filter" size={14} />
            <span>Active filters:</span>
            <div className="flex flex-wrap gap-2">
              {filters.search && (
                <span className="px-2 py-1 bg-primary/10 text-primary rounded-md text-xs">
                  Search: "{filters.search}"
                </span>
              )}
              {filters.marketCap !== 'all' && (
                <span className="px-2 py-1 bg-primary/10 text-primary rounded-md text-xs">
                  {marketCapOptions.find(opt => opt.value === filters.marketCap)?.label}
                </span>
              )}
              {filters.sector !== 'all' && (
                <span className="px-2 py-1 bg-primary/10 text-primary rounded-md text-xs">
                  {sectorOptions.find(opt => opt.value === filters.sector)?.label}
                </span>
              )}
              {filters.proximity !== 'all' && (
                <span className="px-2 py-1 bg-primary/10 text-primary rounded-md text-xs">
                  {proximityOptions.find(opt => opt.value === filters.proximity)?.label}
                </span>
              )}
              {(filters.minPrice || filters.maxPrice) && (
                <span className="px-2 py-1 bg-primary/10 text-primary rounded-md text-xs">
                  Price: ₹{filters.minPrice || '0'} - ₹{filters.maxPrice || '∞'}
                </span>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default FilterControls;