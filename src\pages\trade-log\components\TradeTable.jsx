import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';
import Select from '../../../components/ui/Select';


const TradeTable = ({ trades, onSort, sortConfig }) => {
  const [expandedRows, setExpandedRows] = useState(new Set());
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  const toggleRowExpansion = (tradeId) => {
    const newExpanded = new Set(expandedRows);
    if (newExpanded.has(tradeId)) {
      newExpanded.delete(tradeId);
    } else {
      newExpanded.add(tradeId);
    }
    setExpandedRows(newExpanded);
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount);
  };

  const formatDate = (date) => {
    return new Date(date).toLocaleDateString('en-IN', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  const formatTime = (date) => {
    return new Date(date).toLocaleTimeString('en-IN', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    });
  };

  const calculateHoldingPeriod = (buyDate, sellDate) => {
    if (!sellDate) return 'Holding';
    const days = Math.floor((new Date(sellDate) - new Date(buyDate)) / (1000 * 60 * 60 * 24));
    if (days === 0) return 'Intraday';
    if (days === 1) return '1 day';
    return `${days} days`;
  };

  const getSortIcon = (column) => {
    if (sortConfig.key !== column) {
      return <Icon name="ArrowUpDown" size={14} className="opacity-50" />;
    }
    return sortConfig.direction === 'asc' 
      ? <Icon name="ArrowUp" size={14} />
      : <Icon name="ArrowDown" size={14} />;
  };

  const totalPages = Math.ceil(trades.length / pageSize);
  const startIndex = (currentPage - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const currentTrades = trades.slice(startIndex, endIndex);

  const columns = [
    { key: 'symbol', label: 'Symbol', sortable: true },
    { key: 'type', label: 'Type', sortable: true },
    { key: 'buyDate', label: 'Buy Date', sortable: true },
    { key: 'sellDate', label: 'Sell Date', sortable: true },
    { key: 'quantity', label: 'Qty', sortable: true },
    { key: 'buyPrice', label: 'Buy Price', sortable: true },
    { key: 'sellPrice', label: 'Sell Price', sortable: true },
    { key: 'pnl', label: 'P&L', sortable: true },
    { key: 'pnlPercent', label: 'P&L %', sortable: true },
    { key: 'holdingPeriod', label: 'Holding', sortable: false },
    { key: 'actions', label: '', sortable: false }
  ];

  return (
    <div className="bg-card border border-border rounded-lg overflow-hidden">
      {/* Table Header */}
      <div className="px-6 py-4 border-b border-border bg-muted/50">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-foreground">Trade History</h3>
          <div className="flex items-center space-x-4">
            <span className="text-sm text-muted-foreground">
              Showing {startIndex + 1}-{Math.min(endIndex, trades.length)} of {trades.length} trades
            </span>
            <Select
              options={[
                { value: 10, label: '10 per page' },
                { value: 25, label: '25 per page' },
                { value: 50, label: '50 per page' },
                { value: 100, label: '100 per page' }
              ]}
              value={pageSize}
              onChange={setPageSize}
            />
          </div>
        </div>
      </div>

      {/* Desktop Table */}
      <div className="hidden lg:block overflow-x-auto">
        <table className="w-full">
          <thead className="bg-muted/30">
            <tr>
              {columns.map((column) => (
                <th
                  key={column.key}
                  className={`px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider ${
                    column.sortable ? 'cursor-pointer hover:bg-muted/50' : ''
                  }`}
                  onClick={() => column.sortable && onSort(column.key)}
                >
                  <div className="flex items-center space-x-1">
                    <span>{column.label}</span>
                    {column.sortable && getSortIcon(column.key)}
                  </div>
                </th>
              ))}
            </tr>
          </thead>
          <tbody className="divide-y divide-border">
            {currentTrades.map((trade) => (
              <React.Fragment key={trade.id}>
                <tr className="hover:bg-muted/30 transition-colors">
                  <td className="px-4 py-3">
                    <div className="font-medium text-foreground">{trade.symbol}</div>
                  </td>
                  <td className="px-4 py-3">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      trade.type === 'BUY' ?'bg-success/10 text-success' :'bg-error/10 text-error'
                    }`}>
                      {trade.type}
                    </span>
                  </td>
                  <td className="px-4 py-3">
                    <div className="text-sm text-foreground">{formatDate(trade.buyDate)}</div>
                    <div className="text-xs text-muted-foreground">{formatTime(trade.buyDate)}</div>
                  </td>
                  <td className="px-4 py-3">
                    {trade.sellDate ? (
                      <>
                        <div className="text-sm text-foreground">{formatDate(trade.sellDate)}</div>
                        <div className="text-xs text-muted-foreground">{formatTime(trade.sellDate)}</div>
                      </>
                    ) : (
                      <span className="text-sm text-muted-foreground">-</span>
                    )}
                  </td>
                  <td className="px-4 py-3 text-sm font-data">{trade.quantity}</td>
                  <td className="px-4 py-3 text-sm font-data">{formatCurrency(trade.buyPrice)}</td>
                  <td className="px-4 py-3 text-sm font-data">
                    {trade.sellPrice ? formatCurrency(trade.sellPrice) : '-'}
                  </td>
                  <td className="px-4 py-3">
                    <span className={`text-sm font-medium font-data ${
                      trade.pnl > 0 ? 'text-success' : trade.pnl < 0 ? 'text-error' : 'text-muted-foreground'
                    }`}>
                      {formatCurrency(trade.pnl)}
                    </span>
                  </td>
                  <td className="px-4 py-3">
                    <span className={`text-sm font-medium font-data ${
                      trade.pnlPercent > 0 ? 'text-success' : trade.pnlPercent < 0 ? 'text-error' : 'text-muted-foreground'
                    }`}>
                      {trade.pnlPercent > 0 ? '+' : ''}{trade.pnlPercent.toFixed(2)}%
                    </span>
                  </td>
                  <td className="px-4 py-3 text-sm text-muted-foreground">
                    {calculateHoldingPeriod(trade.buyDate, trade.sellDate)}
                  </td>
                  <td className="px-4 py-3">
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => toggleRowExpansion(trade.id)}
                    >
                      <Icon 
                        name={expandedRows.has(trade.id) ? "ChevronUp" : "ChevronDown"} 
                        size={16} 
                      />
                    </Button>
                  </td>
                </tr>
                
                {/* Expanded Row Details */}
                {expandedRows.has(trade.id) && (
                  <tr>
                    <td colSpan={columns.length} className="px-4 py-4 bg-muted/20">
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <div className="space-y-2">
                          <h4 className="text-sm font-medium text-foreground">OHLC Data</h4>
                          <div className="text-xs space-y-1">
                            <div className="flex justify-between">
                              <span className="text-muted-foreground">Open:</span>
                              <span className="font-data">{formatCurrency(trade.ohlc.open)}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-muted-foreground">High:</span>
                              <span className="font-data">{formatCurrency(trade.ohlc.high)}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-muted-foreground">Low:</span>
                              <span className="font-data">{formatCurrency(trade.ohlc.low)}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-muted-foreground">Close:</span>
                              <span className="font-data">{formatCurrency(trade.ohlc.close)}</span>
                            </div>
                          </div>
                        </div>
                        
                        <div className="space-y-2">
                          <h4 className="text-sm font-medium text-foreground">GTT Details</h4>
                          <div className="text-xs space-y-1">
                            <div className="flex justify-between">
                              <span className="text-muted-foreground">GTT ID:</span>
                              <span className="font-data">{trade.gttOrderId}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-muted-foreground">Trigger Price:</span>
                              <span className="font-data">{formatCurrency(trade.triggerPrice)}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-muted-foreground">Target Price:</span>
                              <span className="font-data">{formatCurrency(trade.targetPrice)}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-muted-foreground">Status:</span>
                              <span className={`px-2 py-1 rounded-full text-xs ${
                                trade.status === 'COMPLETED' ? 'bg-success/10 text-success' :
                                trade.status === 'ACTIVE'? 'bg-accent/10 text-accent' : 'bg-muted text-muted-foreground'
                              }`}>
                                {trade.status}
                              </span>
                            </div>
                          </div>
                        </div>
                        
                        <div className="space-y-2">
                          <h4 className="text-sm font-medium text-foreground">Trade Summary</h4>
                          <div className="text-xs space-y-1">
                            <div className="flex justify-between">
                              <span className="text-muted-foreground">Investment:</span>
                              <span className="font-data">{formatCurrency(trade.buyPrice * trade.quantity)}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-muted-foreground">Current Value:</span>
                              <span className="font-data">
                                {trade.sellPrice 
                                  ? formatCurrency(trade.sellPrice * trade.quantity)
                                  : formatCurrency(trade.currentPrice * trade.quantity)
                                }
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-muted-foreground">Brokerage:</span>
                              <span className="font-data">{formatCurrency(trade.brokerage || 0)}</span>
                            </div>
                            <div className="flex justify-between font-medium">
                              <span className="text-foreground">Net P&L:</span>
                              <span className={`font-data ${
                                trade.pnl > 0 ? 'text-success' : trade.pnl < 0 ? 'text-error' : 'text-muted-foreground'
                              }`}>
                                {formatCurrency(trade.pnl)}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </td>
                  </tr>
                )}
              </React.Fragment>
            ))}
          </tbody>
        </table>
      </div>

      {/* Mobile Cards */}
      <div className="lg:hidden divide-y divide-border">
        {currentTrades.map((trade) => (
          <div key={trade.id} className="p-4">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center space-x-3">
                <h4 className="font-medium text-foreground">{trade.symbol}</h4>
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                  trade.type === 'BUY' ?'bg-success/10 text-success' :'bg-error/10 text-error'
                }`}>
                  {trade.type}
                </span>
              </div>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => toggleRowExpansion(trade.id)}
              >
                <Icon 
                  name={expandedRows.has(trade.id) ? "ChevronUp" : "ChevronDown"} 
                  size={16} 
                />
              </Button>
            </div>
            
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-muted-foreground">Buy Date:</span>
                <div className="font-data">{formatDate(trade.buyDate)}</div>
              </div>
              <div>
                <span className="text-muted-foreground">Quantity:</span>
                <div className="font-data">{trade.quantity}</div>
              </div>
              <div>
                <span className="text-muted-foreground">Buy Price:</span>
                <div className="font-data">{formatCurrency(trade.buyPrice)}</div>
              </div>
              <div>
                <span className="text-muted-foreground">P&L:</span>
                <div className={`font-data font-medium ${
                  trade.pnl > 0 ? 'text-success' : trade.pnl < 0 ? 'text-error' : 'text-muted-foreground'
                }`}>
                  {formatCurrency(trade.pnl)} ({trade.pnlPercent > 0 ? '+' : ''}{trade.pnlPercent.toFixed(2)}%)
                </div>
              </div>
            </div>

            {expandedRows.has(trade.id) && (
              <div className="mt-4 pt-4 border-t border-border">
                <div className="grid grid-cols-1 gap-4">
                  <div>
                    <h5 className="text-sm font-medium text-foreground mb-2">OHLC Data</h5>
                    <div className="grid grid-cols-2 gap-2 text-xs">
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Open:</span>
                        <span className="font-data">{formatCurrency(trade.ohlc.open)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">High:</span>
                        <span className="font-data">{formatCurrency(trade.ohlc.high)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Low:</span>
                        <span className="font-data">{formatCurrency(trade.ohlc.low)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Close:</span>
                        <span className="font-data">{formatCurrency(trade.ohlc.close)}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="px-6 py-4 border-t border-border bg-muted/30">
          <div className="flex items-center justify-between">
            <div className="text-sm text-muted-foreground">
              Page {currentPage} of {totalPages}
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                disabled={currentPage === 1}
                iconName="ChevronLeft"
                iconPosition="left"
              >
                Previous
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                disabled={currentPage === totalPages}
                iconName="ChevronRight"
                iconPosition="right"
              >
                Next
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TradeTable;