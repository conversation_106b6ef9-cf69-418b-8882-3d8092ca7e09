import React, { useState } from 'react';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, AreaChart, Area } from 'recharts';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const HistoricalChart = () => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [chartType, setChartType] = useState('allocation'); // 'allocation' or 'usage'
  const [timeRange, setTimeRange] = useState('30d'); // '7d', '30d', '90d'

  // Mock historical data
  const allocationData = [
    { date: '2025-01-01', totalCapital: 100000, usedCapital: 0, availableCapital: 100000 },
    { date: '2025-01-05', totalCapital: 100000, usedCapital: 15000, availableCapital: 85000 },
    { date: '2025-01-10', totalCapital: 120000, usedCapital: 25000, availableCapital: 95000 },
    { date: '2025-01-15', totalCapital: 120000, usedCapital: 45000, availableCapital: 75000 },
    { date: '2025-01-20', totalCapital: 150000, usedCapital: 68000, availableCapital: 82000 },
    { date: '2025-01-25', totalCapital: 150000, usedCapital: 85000, availableCapital: 65000 },
    { date: '2025-01-30', totalCapital: 150000, usedCapital: 102000, availableCapital: 48000 }
  ];

  const usageData = [
    { date: '2025-01-01', percentage: 0, stocks: 0 },
    { date: '2025-01-05', percentage: 15, stocks: 2 },
    { date: '2025-01-10', percentage: 20.8, stocks: 3 },
    { date: '2025-01-15', percentage: 37.5, stocks: 5 },
    { date: '2025-01-20', percentage: 45.3, stocks: 7 },
    { date: '2025-01-25', percentage: 56.7, stocks: 8 },
    { date: '2025-01-30', percentage: 68, stocks: 10 }
  ];

  const formatCurrency = (value) => {
    return `₹${(value / 1000).toFixed(0)}K`;
  };

  const formatDate = (dateStr) => {
    const date = new Date(dateStr);
    return date.toLocaleDateString('en-IN', { month: 'short', day: 'numeric' });
  };

  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-popover border border-border rounded-lg shadow-trading-lg p-3">
          <p className="text-sm font-medium text-foreground mb-2">
            {formatDate(label)}
          </p>
          {payload.map((entry, index) => (
            <div key={index} className="flex items-center space-x-2">
              <div 
                className="w-3 h-3 rounded-full" 
                style={{ backgroundColor: entry.color }}
              />
              <span className="text-sm text-muted-foreground">{entry.name}:</span>
              <span className="text-sm font-medium text-foreground">
                {chartType === 'allocation' 
                  ? formatCurrency(entry.value)
                  : entry.dataKey === 'percentage' 
                    ? `${entry.value.toFixed(1)}%`
                    : entry.value
                }
              </span>
            </div>
          ))}
        </div>
      );
    }
    return null;
  };

  return (
    <div className="bg-card rounded-lg border border-border">
      <div className="p-6 border-b border-border">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-accent/10 rounded-lg flex items-center justify-center">
              <Icon name="TrendingUp" size={20} color="var(--color-accent)" />
            </div>
            <div>
              <h2 className="text-lg font-semibold text-foreground">Capital Allocation Trends</h2>
              <p className="text-sm text-muted-foreground">Historical capital usage patterns</p>
            </div>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsExpanded(!isExpanded)}
            iconName={isExpanded ? "ChevronUp" : "ChevronDown"}
            iconPosition="left"
          >
            {isExpanded ? 'Collapse' : 'Expand'}
          </Button>
        </div>
      </div>

      {isExpanded && (
        <div className="p-6">
          {/* Chart Controls */}
          <div className="flex flex-wrap items-center justify-between gap-4 mb-6">
            <div className="flex items-center space-x-2">
              <span className="text-sm text-muted-foreground">Chart Type:</span>
              <div className="flex rounded-lg border border-border overflow-hidden">
                <button
                  onClick={() => setChartType('allocation')}
                  className={`px-3 py-1 text-sm font-medium transition-colors ${
                    chartType === 'allocation' ?'bg-primary text-primary-foreground' :'bg-background text-muted-foreground hover:text-foreground'
                  }`}
                >
                  Capital
                </button>
                <button
                  onClick={() => setChartType('usage')}
                  className={`px-3 py-1 text-sm font-medium transition-colors ${
                    chartType === 'usage' ?'bg-primary text-primary-foreground' :'bg-background text-muted-foreground hover:text-foreground'
                  }`}
                >
                  Usage %
                </button>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <span className="text-sm text-muted-foreground">Period:</span>
              <div className="flex rounded-lg border border-border overflow-hidden">
                {['7d', '30d', '90d'].map((range) => (
                  <button
                    key={range}
                    onClick={() => setTimeRange(range)}
                    className={`px-3 py-1 text-sm font-medium transition-colors ${
                      timeRange === range
                        ? 'bg-primary text-primary-foreground'
                        : 'bg-background text-muted-foreground hover:text-foreground'
                    }`}
                  >
                    {range}
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* Chart */}
          <div className="h-80 w-full">
            <ResponsiveContainer width="100%" height="100%">
              {chartType === 'allocation' ? (
                <AreaChart data={allocationData}>
                  <CartesianGrid strokeDasharray="3 3" stroke="var(--color-border)" />
                  <XAxis 
                    dataKey="date" 
                    tickFormatter={formatDate}
                    stroke="var(--color-muted-foreground)"
                    fontSize={12}
                  />
                  <YAxis 
                    tickFormatter={formatCurrency}
                    stroke="var(--color-muted-foreground)"
                    fontSize={12}
                  />
                  <Tooltip content={<CustomTooltip />} />
                  <Area
                    type="monotone"
                    dataKey="totalCapital"
                    stackId="1"
                    stroke="var(--color-primary)"
                    fill="var(--color-primary)"
                    fillOpacity={0.1}
                    name="Total Capital"
                  />
                  <Area
                    type="monotone"
                    dataKey="usedCapital"
                    stackId="2"
                    stroke="var(--color-error)"
                    fill="var(--color-error)"
                    fillOpacity={0.3}
                    name="Used Capital"
                  />
                  <Area
                    type="monotone"
                    dataKey="availableCapital"
                    stackId="3"
                    stroke="var(--color-success)"
                    fill="var(--color-success)"
                    fillOpacity={0.2}
                    name="Available Capital"
                  />
                </AreaChart>
              ) : (
                <LineChart data={usageData}>
                  <CartesianGrid strokeDasharray="3 3" stroke="var(--color-border)" />
                  <XAxis 
                    dataKey="date" 
                    tickFormatter={formatDate}
                    stroke="var(--color-muted-foreground)"
                    fontSize={12}
                  />
                  <YAxis 
                    stroke="var(--color-muted-foreground)"
                    fontSize={12}
                  />
                  <Tooltip content={<CustomTooltip />} />
                  <Line
                    type="monotone"
                    dataKey="percentage"
                    stroke="var(--color-accent)"
                    strokeWidth={3}
                    dot={{ fill: 'var(--color-accent)', strokeWidth: 2, r: 4 }}
                    name="Usage Percentage"
                  />
                  <Line
                    type="monotone"
                    dataKey="stocks"
                    stroke="var(--color-success)"
                    strokeWidth={2}
                    dot={{ fill: 'var(--color-success)', strokeWidth: 2, r: 3 }}
                    name="Active Stocks"
                  />
                </LineChart>
              )}
            </ResponsiveContainer>
          </div>

          {/* Chart Legend */}
          <div className="flex flex-wrap items-center justify-center gap-6 mt-4 pt-4 border-t border-border">
            {chartType === 'allocation' ? (
              <>
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 rounded-full bg-primary" />
                  <span className="text-sm text-muted-foreground">Total Capital</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 rounded-full bg-error" />
                  <span className="text-sm text-muted-foreground">Used Capital</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 rounded-full bg-success" />
                  <span className="text-sm text-muted-foreground">Available Capital</span>
                </div>
              </>
            ) : (
              <>
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 rounded-full bg-accent" />
                  <span className="text-sm text-muted-foreground">Usage Percentage</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 rounded-full bg-success" />
                  <span className="text-sm text-muted-foreground">Active Stocks</span>
                </div>
              </>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default HistoricalChart;