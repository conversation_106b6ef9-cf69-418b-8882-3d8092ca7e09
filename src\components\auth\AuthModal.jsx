import React, { useState } from 'react';
import Login from './Login';
import Signup from './Signup';

const AuthModal = () => {
  const [isLogin, setIsLogin] = useState(true);

  const toggleMode = () => {
    setIsLogin(!isLogin);
  };

  return (
    <>
      {isLogin ? (
        <Login onToggle={toggleMode} />
      ) : (
        <Signup onToggle={toggleMode} />
      )}
    </>
  );
};

export default AuthModal;