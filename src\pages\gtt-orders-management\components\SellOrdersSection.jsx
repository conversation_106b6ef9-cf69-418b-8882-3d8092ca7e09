import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const SellOrdersSection = ({ sellOrders, onModifyOrder, onDeleteOrder, onCleanupExpired }) => {
  const [sortBy, setSortBy] = useState('symbol');
  const [sortOrder, setSortOrder] = useState('asc');
  const [selectedOrders, setSelectedOrders] = useState([]);
  const [filterStatus, setFilterStatus] = useState('all');

  const filteredOrders = sellOrders.filter(order => {
    if (filterStatus === 'all') return true;
    return order.status.toLowerCase() === filterStatus;
  });

  const sortedOrders = [...filteredOrders].sort((a, b) => {
    const aValue = a[sortBy];
    const bValue = b[sortBy];
    
    if (typeof aValue === 'number') {
      return sortOrder === 'asc' ? aValue - bValue : bValue - aValue;
    }
    
    return sortOrder === 'asc' 
      ? aValue.localeCompare(bValue)
      : bValue.localeCompare(aValue);
  });

  const handleSort = (field) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortOrder('asc');
    }
  };

  const handleSelectAll = (checked) => {
    setSelectedOrders(checked ? filteredOrders.map(order => order.id) : []);
  };

  const handleSelectOrder = (orderId, checked) => {
    if (checked) {
      setSelectedOrders([...selectedOrders, orderId]);
    } else {
      setSelectedOrders(selectedOrders.filter(id => id !== orderId));
    }
  };

  const handleBulkDelete = () => {
    selectedOrders.forEach(orderId => {
      onDeleteOrder(orderId);
    });
    setSelectedOrders([]);
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 2
    }).format(amount);
  };

  const formatPercentage = (value) => {
    const sign = value >= 0 ? '+' : '';
    return `${sign}${value.toFixed(2)}%`;
  };

  const getSortIcon = (field) => {
    if (sortBy !== field) return 'ArrowUpDown';
    return sortOrder === 'asc' ? 'ArrowUp' : 'ArrowDown';
  };

  const getStatusColor = (status) => {
    switch (status.toLowerCase()) {
      case 'pending':
        return 'bg-warning/10 text-warning';
      case 'triggered':
        return 'bg-success/10 text-success';
      case 'expired':
        return 'bg-error/10 text-error';
      case 'cancelled':
        return 'bg-muted text-muted-foreground';
      default:
        return 'bg-muted text-muted-foreground';
    }
  };

  const getOrderTypeColor = (type) => {
    switch (type.toLowerCase()) {
      case 'target':
        return 'bg-success/10 text-success';
      case 'stop-loss':
        return 'bg-error/10 text-error';
      default:
        return 'bg-muted text-muted-foreground';
    }
  };

  const calculatePriceDifference = (currentPrice, triggerPrice) => {
    return ((triggerPrice - currentPrice) / currentPrice) * 100;
  };

  const expiredOrdersCount = sellOrders.filter(order => order.status.toLowerCase() === 'expired').length;

  return (
    <div className="bg-card rounded-lg border border-border">
      <div className="p-6 border-b border-border">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-warning/10 rounded-lg flex items-center justify-center">
              <Icon name="TrendingDown" size={20} color="var(--color-warning)" />
            </div>
            <div>
              <h2 className="text-lg font-semibold text-foreground">Pending Sell Orders</h2>
              <p className="text-sm text-muted-foreground">
                {sellOrders.length} GTT sell orders with price monitoring
              </p>
            </div>
          </div>
          
          <div className="flex items-center space-x-3">
            {expiredOrdersCount > 0 && (
              <Button
                variant="outline"
                size="sm"
                onClick={onCleanupExpired}
                iconName="Trash2"
                iconPosition="left"
              >
                Cleanup Expired ({expiredOrdersCount})
              </Button>
            )}
            
            {selectedOrders.length > 0 && (
              <div className="flex items-center space-x-2">
                <span className="text-sm text-muted-foreground">
                  {selectedOrders.length} selected
                </span>
                <Button
                  variant="destructive"
                  size="sm"
                  onClick={handleBulkDelete}
                  iconName="Trash2"
                  iconPosition="left"
                >
                  Delete Selected
                </Button>
              </div>
            )}
          </div>
        </div>

        {/* Filter Controls */}
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <span className="text-sm text-muted-foreground">Filter:</span>
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              className="text-sm border border-border rounded-md px-3 py-1 bg-background"
            >
              <option value="all">All Orders</option>
              <option value="pending">Pending</option>
              <option value="triggered">Triggered</option>
              <option value="expired">Expired</option>
              <option value="cancelled">Cancelled</option>
            </select>
          </div>
        </div>
      </div>

      <div className="p-6">
        {sortedOrders.length === 0 ? (
          <div className="text-center py-12">
            <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center mx-auto mb-4">
              <Icon name="TrendingDown" size={24} color="var(--color-muted-foreground)" />
            </div>
            <h3 className="text-lg font-medium text-foreground mb-2">
              {filterStatus === 'all' ? 'No Sell Orders' : `No ${filterStatus.charAt(0).toUpperCase() + filterStatus.slice(1)} Orders`}
            </h3>
            <p className="text-muted-foreground mb-4">
              Sell orders will appear here when holdings reach target prices
            </p>
          </div>
        ) : (
          <>
            {/* Desktop Table */}
            <div className="hidden md:block overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-border">
                    <th className="text-left py-3 px-2">
                      <input
                        type="checkbox"
                        checked={selectedOrders.length === filteredOrders.length && filteredOrders.length > 0}
                        onChange={(e) => handleSelectAll(e.target.checked)}
                        className="rounded border-border"
                      />
                    </th>
                    <th className="text-left py-3 px-4">
                      <button
                        onClick={() => handleSort('symbol')}
                        className="flex items-center space-x-1 text-sm font-medium text-muted-foreground hover:text-foreground"
                      >
                        <span>Symbol</span>
                        <Icon name={getSortIcon('symbol')} size={14} />
                      </button>
                    </th>
                    <th className="text-left py-3 px-4">
                      <button
                        onClick={() => handleSort('quantity')}
                        className="flex items-center space-x-1 text-sm font-medium text-muted-foreground hover:text-foreground"
                      >
                        <span>Quantity</span>
                        <Icon name={getSortIcon('quantity')} size={14} />
                      </button>
                    </th>
                    <th className="text-left py-3 px-4">
                      <button
                        onClick={() => handleSort('triggerPrice')}
                        className="flex items-center space-x-1 text-sm font-medium text-muted-foreground hover:text-foreground"
                      >
                        <span>Trigger Price</span>
                        <Icon name={getSortIcon('triggerPrice')} size={14} />
                      </button>
                    </th>
                    <th className="text-left py-3 px-4">
                      <button
                        onClick={() => handleSort('currentPrice')}
                        className="flex items-center space-x-1 text-sm font-medium text-muted-foreground hover:text-foreground"
                      >
                        <span>Current Price</span>
                        <Icon name={getSortIcon('currentPrice')} size={14} />
                      </button>
                    </th>
                    <th className="text-left py-3 px-4">
                      <span className="text-sm font-medium text-muted-foreground">Difference</span>
                    </th>
                    <th className="text-left py-3 px-4">
                      <span className="text-sm font-medium text-muted-foreground">Type</span>
                    </th>
                    <th className="text-left py-3 px-4">
                      <span className="text-sm font-medium text-muted-foreground">Status</span>
                    </th>
                    <th className="text-right py-3 px-4">
                      <span className="text-sm font-medium text-muted-foreground">Actions</span>
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {sortedOrders.map((order) => {
                    const priceDifference = calculatePriceDifference(order.currentPrice, order.triggerPrice);
                    const isNearTrigger = Math.abs(priceDifference) <= 2;
                    
                    return (
                      <tr 
                        key={order.id} 
                        className={`border-b border-border hover:bg-muted/50 ${
                          isNearTrigger ? 'bg-warning/5' : ''
                        }`}
                      >
                        <td className="py-4 px-2">
                          <input
                            type="checkbox"
                            checked={selectedOrders.includes(order.id)}
                            onChange={(e) => handleSelectOrder(order.id, e.target.checked)}
                            className="rounded border-border"
                          />
                        </td>
                        <td className="py-4 px-4">
                          <div className="flex items-center space-x-3">
                            <div className="w-8 h-8 bg-warning/10 rounded-lg flex items-center justify-center">
                              <span className="text-xs font-medium text-warning">
                                {order.symbol.charAt(0)}
                              </span>
                            </div>
                            <div>
                              <p className="font-medium text-foreground">{order.symbol}</p>
                              <p className="text-xs text-muted-foreground">{order.exchange}</p>
                            </div>
                            {isNearTrigger && (
                              <div className="w-2 h-2 bg-warning rounded-full animate-pulse-trading" />
                            )}
                          </div>
                        </td>
                        <td className="py-4 px-4">
                          <div className="text-sm">
                            <p className="font-data font-medium text-foreground">
                              {order.quantity}
                            </p>
                            <p className="text-xs text-muted-foreground">shares</p>
                          </div>
                        </td>
                        <td className="py-4 px-4">
                          <div className="text-sm">
                            <p className="font-data font-medium text-foreground">
                              {formatCurrency(order.triggerPrice)}
                            </p>
                          </div>
                        </td>
                        <td className="py-4 px-4">
                          <div className="text-sm">
                            <p className="font-data font-medium text-foreground">
                              {formatCurrency(order.currentPrice)}
                            </p>
                            <p className={`text-xs ${
                              order.priceChange >= 0 ? 'text-success' : 'text-error'
                            }`}>
                              {formatPercentage(order.priceChange)}
                            </p>
                          </div>
                        </td>
                        <td className="py-4 px-4">
                          <div className="text-sm">
                            <p className={`font-data font-medium ${
                              priceDifference >= 0 ? 'text-success' : 'text-error'
                            }`}>
                              {formatPercentage(priceDifference)}
                            </p>
                            <p className="text-xs text-muted-foreground">
                              {priceDifference >= 0 ? 'Above' : 'Below'} trigger
                            </p>
                          </div>
                        </td>
                        <td className="py-4 px-4">
                          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                            getOrderTypeColor(order.orderType)
                          }`}>
                            {order.orderType}
                          </span>
                        </td>
                        <td className="py-4 px-4">
                          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                            getStatusColor(order.status)
                          }`}>
                            {order.status}
                          </span>
                        </td>
                        <td className="py-4 px-4 text-right">
                          <div className="flex items-center justify-end space-x-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => onModifyOrder(order)}
                              iconName="Edit"
                              iconPosition="left"
                              disabled={order.status.toLowerCase() === 'triggered'}
                            >
                              Modify
                            </Button>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => onDeleteOrder(order.id)}
                              disabled={order.status.toLowerCase() === 'triggered'}
                            >
                              <Icon name="Trash2" size={16} />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>

            {/* Mobile Cards */}
            <div className="md:hidden space-y-4">
              {sortedOrders.map((order) => {
                const priceDifference = calculatePriceDifference(order.currentPrice, order.triggerPrice);
                const isNearTrigger = Math.abs(priceDifference) <= 2;
                
                return (
                  <div 
                    key={order.id} 
                    className={`bg-muted/50 rounded-lg p-4 ${
                      isNearTrigger ? 'ring-2 ring-warning/20' : ''
                    }`}
                  >
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-3">
                        <input
                          type="checkbox"
                          checked={selectedOrders.includes(order.id)}
                          onChange={(e) => handleSelectOrder(order.id, e.target.checked)}
                          className="rounded border-border"
                        />
                        <div className="w-8 h-8 bg-warning/10 rounded-lg flex items-center justify-center">
                          <span className="text-xs font-medium text-warning">
                            {order.symbol.charAt(0)}
                          </span>
                        </div>
                        <div>
                          <p className="font-medium text-foreground">{order.symbol}</p>
                          <p className="text-xs text-muted-foreground">{order.exchange}</p>
                        </div>
                        {isNearTrigger && (
                          <div className="w-2 h-2 bg-warning rounded-full animate-pulse-trading" />
                        )}
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                          getOrderTypeColor(order.orderType)
                        }`}>
                          {order.orderType}
                        </span>
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                          getStatusColor(order.status)
                        }`}>
                          {order.status}
                        </span>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-2 gap-4 mb-4">
                      <div>
                        <p className="text-xs text-muted-foreground mb-1">Quantity</p>
                        <p className="font-data font-medium text-foreground">
                          {order.quantity}
                        </p>
                      </div>
                      <div>
                        <p className="text-xs text-muted-foreground mb-1">Trigger Price</p>
                        <p className="font-data font-medium text-foreground">
                          {formatCurrency(order.triggerPrice)}
                        </p>
                      </div>
                      <div>
                        <p className="text-xs text-muted-foreground mb-1">Current Price</p>
                        <p className="font-data font-medium text-foreground">
                          {formatCurrency(order.currentPrice)}
                        </p>
                      </div>
                      <div>
                        <p className="text-xs text-muted-foreground mb-1">Difference</p>
                        <p className={`font-data font-medium ${
                          priceDifference >= 0 ? 'text-success' : 'text-error'
                        }`}>
                          {formatPercentage(priceDifference)}
                        </p>
                      </div>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div className="text-xs text-muted-foreground">
                        Created: {order.createdDate}
                      </div>
                      <div className="flex items-center space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => onModifyOrder(order)}
                          iconName="Edit"
                          iconPosition="left"
                          disabled={order.status.toLowerCase() === 'triggered'}
                        >
                          Modify
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => onDeleteOrder(order.id)}
                          disabled={order.status.toLowerCase() === 'triggered'}
                        >
                          <Icon name="Trash2" size={16} />
                        </Button>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default SellOrdersSection;