import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const BuyHoldingsSection = ({ buyHoldings, onModifyOrder, onDeleteOrder }) => {
  const [sortBy, setSortBy] = useState('symbol');
  const [sortOrder, setSortOrder] = useState('asc');
  const [selectedHoldings, setSelectedHoldings] = useState([]);
  const [expandedRows, setExpandedRows] = useState([]);

  const sortedHoldings = [...buyHoldings].sort((a, b) => {
    const aValue = a[sortBy];
    const bValue = b[sortBy];
    
    if (typeof aValue === 'number') {
      return sortOrder === 'asc' ? aValue - bValue : bValue - aValue;
    }
    
    return sortOrder === 'asc' 
      ? aValue.localeCompare(bValue)
      : bValue.localeCompare(aValue);
  });

  const handleSort = (field) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortOrder('asc');
    }
  };

  const toggleRowExpansion = (holdingId) => {
    setExpandedRows(prev => 
      prev.includes(holdingId) 
        ? prev.filter(id => id !== holdingId)
        : [...prev, holdingId]
    );
  };

  const handleSelectAll = (checked) => {
    setSelectedHoldings(checked ? buyHoldings.map(holding => holding.id) : []);
  };

  const handleSelectHolding = (holdingId, checked) => {
    if (checked) {
      setSelectedHoldings([...selectedHoldings, holdingId]);
    } else {
      setSelectedHoldings(selectedHoldings.filter(id => id !== holdingId));
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 2
    }).format(amount);
  };

  const formatPercentage = (value) => {
    const sign = value >= 0 ? '+' : '';
    return `${sign}${value.toFixed(2)}%`;
  };

  const getSortIcon = (field) => {
    if (sortBy !== field) return 'ArrowUpDown';
    return sortOrder === 'asc' ? 'ArrowUp' : 'ArrowDown';
  };

  const calculatePnL = (holding) => {
    const currentValue = holding.currentPrice * holding.totalQuantity;
    const investedValue = holding.averagePrice * holding.totalQuantity;
    return currentValue - investedValue;
  };

  const calculatePnLPercentage = (holding) => {
    const pnl = calculatePnL(holding);
    const investedValue = holding.averagePrice * holding.totalQuantity;
    return (pnl / investedValue) * 100;
  };

  return (
    <div className="bg-card rounded-lg border border-border">
      <div className="p-6 border-b border-border">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-accent/10 rounded-lg flex items-center justify-center">
              <Icon name="Package" size={20} color="var(--color-accent)" />
            </div>
            <div>
              <h2 className="text-lg font-semibold text-foreground">Current Buy Holdings</h2>
              <p className="text-sm text-muted-foreground">
                {buyHoldings.length} positions with target calculations
              </p>
            </div>
          </div>
          
          {selectedHoldings.length > 0 && (
            <div className="flex items-center space-x-2">
              <span className="text-sm text-muted-foreground">
                {selectedHoldings.length} selected
              </span>
              <Button
                variant="outline"
                size="sm"
                iconName="Edit"
                iconPosition="left"
              >
                Bulk Modify
              </Button>
            </div>
          )}
        </div>
      </div>

      <div className="p-6">
        {buyHoldings.length === 0 ? (
          <div className="text-center py-12">
            <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center mx-auto mb-4">
              <Icon name="Package" size={24} color="var(--color-muted-foreground)" />
            </div>
            <h3 className="text-lg font-medium text-foreground mb-2">No Current Holdings</h3>
            <p className="text-muted-foreground mb-4">
              Your buy positions will appear here once GTT orders are executed
            </p>
          </div>
        ) : (
          <>
            {/* Desktop Table */}
            <div className="hidden md:block overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-border">
                    <th className="text-left py-3 px-2">
                      <input
                        type="checkbox"
                        checked={selectedHoldings.length === buyHoldings.length}
                        onChange={(e) => handleSelectAll(e.target.checked)}
                        className="rounded border-border"
                      />
                    </th>
                    <th className="text-left py-3 px-4">
                      <button
                        onClick={() => handleSort('symbol')}
                        className="flex items-center space-x-1 text-sm font-medium text-muted-foreground hover:text-foreground"
                      >
                        <span>Symbol</span>
                        <Icon name={getSortIcon('symbol')} size={14} />
                      </button>
                    </th>
                    <th className="text-left py-3 px-4">
                      <button
                        onClick={() => handleSort('totalQuantity')}
                        className="flex items-center space-x-1 text-sm font-medium text-muted-foreground hover:text-foreground"
                      >
                        <span>Quantity</span>
                        <Icon name={getSortIcon('totalQuantity')} size={14} />
                      </button>
                    </th>
                    <th className="text-left py-3 px-4">
                      <button
                        onClick={() => handleSort('averagePrice')}
                        className="flex items-center space-x-1 text-sm font-medium text-muted-foreground hover:text-foreground"
                      >
                        <span>Avg Price</span>
                        <Icon name={getSortIcon('averagePrice')} size={14} />
                      </button>
                    </th>
                    <th className="text-left py-3 px-4">
                      <button
                        onClick={() => handleSort('currentPrice')}
                        className="flex items-center space-x-1 text-sm font-medium text-muted-foreground hover:text-foreground"
                      >
                        <span>Current Price</span>
                        <Icon name={getSortIcon('currentPrice')} size={14} />
                      </button>
                    </th>
                    <th className="text-left py-3 px-4">
                      <span className="text-sm font-medium text-muted-foreground">Target Price</span>
                    </th>
                    <th className="text-left py-3 px-4">
                      <span className="text-sm font-medium text-muted-foreground">P&L</span>
                    </th>
                    <th className="text-right py-3 px-4">
                      <span className="text-sm font-medium text-muted-foreground">Actions</span>
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {sortedHoldings.map((holding) => {
                    const pnl = calculatePnL(holding);
                    const pnlPercentage = calculatePnLPercentage(holding);
                    const isExpanded = expandedRows.includes(holding.id);
                    
                    return (
                      <React.Fragment key={holding.id}>
                        <tr className="border-b border-border hover:bg-muted/50">
                          <td className="py-4 px-2">
                            <input
                              type="checkbox"
                              checked={selectedHoldings.includes(holding.id)}
                              onChange={(e) => handleSelectHolding(holding.id, e.target.checked)}
                              className="rounded border-border"
                            />
                          </td>
                          <td className="py-4 px-4">
                            <div className="flex items-center space-x-3">
                              <div className="w-8 h-8 bg-accent/10 rounded-lg flex items-center justify-center">
                                <span className="text-xs font-medium text-accent">
                                  {holding.symbol.charAt(0)}
                                </span>
                              </div>
                              <div>
                                <p className="font-medium text-foreground">{holding.symbol}</p>
                                <p className="text-xs text-muted-foreground">{holding.exchange}</p>
                              </div>
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => toggleRowExpansion(holding.id)}
                                className="w-6 h-6"
                              >
                                <Icon 
                                  name={isExpanded ? "ChevronUp" : "ChevronDown"} 
                                  size={14} 
                                />
                              </Button>
                            </div>
                          </td>
                          <td className="py-4 px-4">
                            <div className="text-sm">
                              <p className="font-data font-medium text-foreground">
                                {holding.totalQuantity}
                              </p>
                              <p className="text-xs text-muted-foreground">
                                {holding.entries.length} entries
                              </p>
                            </div>
                          </td>
                          <td className="py-4 px-4">
                            <div className="text-sm">
                              <p className="font-data font-medium text-foreground">
                                {formatCurrency(holding.averagePrice)}
                              </p>
                            </div>
                          </td>
                          <td className="py-4 px-4">
                            <div className="text-sm">
                              <p className="font-data font-medium text-foreground">
                                {formatCurrency(holding.currentPrice)}
                              </p>
                              <p className={`text-xs ${
                                holding.priceChange >= 0 ? 'text-success' : 'text-error'
                              }`}>
                                {formatPercentage(holding.priceChange)}
                              </p>
                            </div>
                          </td>
                          <td className="py-4 px-4">
                            <div className="text-sm">
                              <p className="font-data font-medium text-foreground">
                                {formatCurrency(holding.targetPrice)}
                              </p>
                              <p className="text-xs text-muted-foreground">
                                +6% target
                              </p>
                            </div>
                          </td>
                          <td className="py-4 px-4">
                            <div className="text-sm">
                              <p className={`font-data font-medium ${
                                pnl >= 0 ? 'text-success' : 'text-error'
                              }`}>
                                {formatCurrency(Math.abs(pnl))}
                              </p>
                              <p className={`text-xs ${
                                pnlPercentage >= 0 ? 'text-success' : 'text-error'
                              }`}>
                                {formatPercentage(pnlPercentage)}
                              </p>
                            </div>
                          </td>
                          <td className="py-4 px-4 text-right">
                            <div className="flex items-center justify-end space-x-2">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => onModifyOrder(holding)}
                                iconName="Edit"
                                iconPosition="left"
                              >
                                Modify
                              </Button>
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => onDeleteOrder(holding.id)}
                              >
                                <Icon name="Trash2" size={16} />
                              </Button>
                            </div>
                          </td>
                        </tr>
                        
                        {/* Expanded Row Details */}
                        {isExpanded && (
                          <tr className="bg-muted/30">
                            <td colSpan="8" className="py-4 px-4">
                              <div className="ml-12">
                                <h4 className="text-sm font-medium text-foreground mb-3">Entry Details</h4>
                                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                  {holding.entries.map((entry, index) => (
                                    <div key={index} className="bg-card rounded-lg p-3 border border-border">
                                      <div className="flex items-center justify-between mb-2">
                                        <span className="text-xs text-muted-foreground">
                                          Entry #{index + 1}
                                        </span>
                                        <span className="text-xs text-muted-foreground">
                                          {entry.date}
                                        </span>
                                      </div>
                                      <div className="space-y-1">
                                        <div className="flex justify-between">
                                          <span className="text-xs text-muted-foreground">Quantity:</span>
                                          <span className="text-xs font-data font-medium">{entry.quantity}</span>
                                        </div>
                                        <div className="flex justify-between">
                                          <span className="text-xs text-muted-foreground">Price:</span>
                                          <span className="text-xs font-data font-medium">
                                            {formatCurrency(entry.price)}
                                          </span>
                                        </div>
                                        <div className="flex justify-between">
                                          <span className="text-xs text-muted-foreground">Amount:</span>
                                          <span className="text-xs font-data font-medium">
                                            {formatCurrency(entry.amount)}
                                          </span>
                                        </div>
                                      </div>
                                    </div>
                                  ))}
                                </div>
                              </div>
                            </td>
                          </tr>
                        )}
                      </React.Fragment>
                    );
                  })}
                </tbody>
              </table>
            </div>

            {/* Mobile Cards */}
            <div className="md:hidden space-y-4">
              {sortedHoldings.map((holding) => {
                const pnl = calculatePnL(holding);
                const pnlPercentage = calculatePnLPercentage(holding);
                const isExpanded = expandedRows.includes(holding.id);
                
                return (
                  <div key={holding.id} className="bg-muted/50 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-3">
                        <input
                          type="checkbox"
                          checked={selectedHoldings.includes(holding.id)}
                          onChange={(e) => handleSelectHolding(holding.id, e.target.checked)}
                          className="rounded border-border"
                        />
                        <div className="w-8 h-8 bg-accent/10 rounded-lg flex items-center justify-center">
                          <span className="text-xs font-medium text-accent">
                            {holding.symbol.charAt(0)}
                          </span>
                        </div>
                        <div>
                          <p className="font-medium text-foreground">{holding.symbol}</p>
                          <p className="text-xs text-muted-foreground">{holding.exchange}</p>
                        </div>
                      </div>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => toggleRowExpansion(holding.id)}
                        className="w-6 h-6"
                      >
                        <Icon 
                          name={isExpanded ? "ChevronUp" : "ChevronDown"} 
                          size={14} 
                        />
                      </Button>
                    </div>
                    
                    <div className="grid grid-cols-2 gap-4 mb-4">
                      <div>
                        <p className="text-xs text-muted-foreground mb-1">Quantity</p>
                        <p className="font-data font-medium text-foreground">
                          {holding.totalQuantity}
                        </p>
                      </div>
                      <div>
                        <p className="text-xs text-muted-foreground mb-1">Avg Price</p>
                        <p className="font-data font-medium text-foreground">
                          {formatCurrency(holding.averagePrice)}
                        </p>
                      </div>
                      <div>
                        <p className="text-xs text-muted-foreground mb-1">Current Price</p>
                        <p className="font-data font-medium text-foreground">
                          {formatCurrency(holding.currentPrice)}
                        </p>
                      </div>
                      <div>
                        <p className="text-xs text-muted-foreground mb-1">P&L</p>
                        <p className={`font-data font-medium ${
                          pnl >= 0 ? 'text-success' : 'text-error'
                        }`}>
                          {formatCurrency(Math.abs(pnl))}
                        </p>
                      </div>
                    </div>
                    
                    {isExpanded && (
                      <div className="mb-4 pt-4 border-t border-border">
                        <h4 className="text-sm font-medium text-foreground mb-3">Entry Details</h4>
                        <div className="space-y-2">
                          {holding.entries.map((entry, index) => (
                            <div key={index} className="bg-card rounded-lg p-3 border border-border">
                              <div className="flex items-center justify-between mb-2">
                                <span className="text-xs text-muted-foreground">
                                  Entry #{index + 1}
                                </span>
                                <span className="text-xs text-muted-foreground">
                                  {entry.date}
                                </span>
                              </div>
                              <div className="grid grid-cols-3 gap-2 text-xs">
                                <div>
                                  <span className="text-muted-foreground">Qty:</span>
                                  <p className="font-data font-medium">{entry.quantity}</p>
                                </div>
                                <div>
                                  <span className="text-muted-foreground">Price:</span>
                                  <p className="font-data font-medium">
                                    {formatCurrency(entry.price)}
                                  </p>
                                </div>
                                <div>
                                  <span className="text-muted-foreground">Amount:</span>
                                  <p className="font-data font-medium">
                                    {formatCurrency(entry.amount)}
                                  </p>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                    
                    <div className="flex items-center justify-between">
                      <div className="text-xs text-muted-foreground">
                        Target: {formatCurrency(holding.targetPrice)} (+6%)
                      </div>
                      <div className="flex items-center space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => onModifyOrder(holding)}
                          iconName="Edit"
                          iconPosition="left"
                        >
                          Modify
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => onDeleteOrder(holding.id)}
                        >
                          <Icon name="Trash2" size={16} />
                        </Button>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default BuyHoldingsSection;