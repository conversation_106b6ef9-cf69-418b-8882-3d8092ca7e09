import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';
import Input from '../../../components/ui/Input';

import { Checkbox } from '../../../components/ui/Checkbox';

const NotificationSettings = () => {
  const [emailSettings, setEmailSettings] = useState({
    enabled: true,
    address: '<EMAIL>',
    signalGeneration: true,
    orderExecution: true,
    systemAlerts: true,
    dailyReports: false,
    weeklyReports: true
  });

  const [smsSettings, setSmsSettings] = useState({
    enabled: false,
    phoneNumber: '+91 98765 43210',
    emergencyOnly: true,
    signalGeneration: false,
    orderExecution: true,
    systemAlerts: true
  });

  const [pushSettings, setPushSettings] = useState({
    enabled: true,
    signalGeneration: true,
    orderExecution: true,
    systemAlerts: true,
    priceAlerts: false,
    newsAlerts: false
  });

  const [alertThresholds, setAlertThresholds] = useState({
    profitThreshold: 5000,
    lossThreshold: -2000,
    volumeThreshold: 100000,
    priceChangeThreshold: 5,
    errorRateThreshold: 2
  });

  const [testNotifications, setTestNotifications] = useState({
    email: false,
    sms: false,
    push: false
  });

  const notificationTypes = [
    { value: 'signal_generation', label: 'Signal Generation' },
    { value: 'order_execution', label: 'Order Execution' },
    { value: 'system_alerts', label: 'System Alerts' },
    { value: 'price_alerts', label: 'Price Alerts' },
    { value: 'news_alerts', label: 'News Alerts' },
    { value: 'daily_reports', label: 'Daily Reports' },
    { value: 'weekly_reports', label: 'Weekly Reports' }
  ];

  const handleEmailSettingChange = (key, value) => {
    setEmailSettings(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleSmsSettingChange = (key, value) => {
    setSmsSettings(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handlePushSettingChange = (key, value) => {
    setPushSettings(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleThresholdChange = (key, value) => {
    setAlertThresholds(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const sendTestNotification = async (type) => {
    setTestNotifications(prev => ({
      ...prev,
      [type]: true
    }));

    // Simulate sending test notification
    await new Promise(resolve => setTimeout(resolve, 2000));

    setTestNotifications(prev => ({
      ...prev,
      [type]: false
    }));

    console.log(`Test ${type} notification sent`);
  };

  const saveNotificationSettings = () => {
    const settings = {
      email: emailSettings,
      sms: smsSettings,
      push: pushSettings,
      thresholds: alertThresholds
    };
    console.log('Saving notification settings:', settings);
  };

  const resetNotificationSettings = () => {
    setEmailSettings({
      enabled: true,
      address: '<EMAIL>',
      signalGeneration: true,
      orderExecution: true,
      systemAlerts: true,
      dailyReports: false,
      weeklyReports: true
    });
    setSmsSettings({
      enabled: false,
      phoneNumber: '+91 98765 43210',
      emergencyOnly: true,
      signalGeneration: false,
      orderExecution: true,
      systemAlerts: true
    });
    setPushSettings({
      enabled: true,
      signalGeneration: true,
      orderExecution: true,
      systemAlerts: true,
      priceAlerts: false,
      newsAlerts: false
    });
    setAlertThresholds({
      profitThreshold: 5000,
      lossThreshold: -2000,
      volumeThreshold: 100000,
      priceChangeThreshold: 5,
      errorRateThreshold: 2
    });
  };

  return (
    <div className="space-y-8">
      {/* Email Notifications */}
      <div className="bg-card rounded-lg border border-border p-6">
        <div className="flex items-center space-x-3 mb-6">
          <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
            <Icon name="Mail" size={20} color="var(--color-primary)" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-foreground">Email Notifications</h3>
            <p className="text-sm text-muted-foreground">Configure email alert preferences</p>
          </div>
        </div>

        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <Checkbox
              label="Enable Email Notifications"
              description="Receive notifications via email"
              checked={emailSettings.enabled}
              onChange={(e) => handleEmailSettingChange('enabled', e.target.checked)}
            />
            <Button
              variant="outline"
              size="sm"
              onClick={() => sendTestNotification('email')}
              disabled={!emailSettings.enabled || testNotifications.email}
              loading={testNotifications.email}
              iconName="Send"
              iconPosition="left"
            >
              Test Email
            </Button>
          </div>

          {emailSettings.enabled && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="space-y-4">
                <Input
                  label="Email Address"
                  type="email"
                  value={emailSettings.address}
                  onChange={(e) => handleEmailSettingChange('address', e.target.value)}
                  placeholder="<EMAIL>"
                />
              </div>

              <div className="space-y-4">
                <div className="space-y-3">
                  <h4 className="text-sm font-medium text-foreground">Email Preferences</h4>
                  <Checkbox
                    label="Signal Generation"
                    description="New buy/sell signals"
                    checked={emailSettings.signalGeneration}
                    onChange={(e) => handleEmailSettingChange('signalGeneration', e.target.checked)}
                  />
                  <Checkbox
                    label="Order Execution"
                    description="Order placed/executed confirmations"
                    checked={emailSettings.orderExecution}
                    onChange={(e) => handleEmailSettingChange('orderExecution', e.target.checked)}
                  />
                  <Checkbox
                    label="System Alerts"
                    description="System errors and warnings"
                    checked={emailSettings.systemAlerts}
                    onChange={(e) => handleEmailSettingChange('systemAlerts', e.target.checked)}
                  />
                  <Checkbox
                    label="Daily Reports"
                    description="End of day trading summary"
                    checked={emailSettings.dailyReports}
                    onChange={(e) => handleEmailSettingChange('dailyReports', e.target.checked)}
                  />
                  <Checkbox
                    label="Weekly Reports"
                    description="Weekly performance summary"
                    checked={emailSettings.weeklyReports}
                    onChange={(e) => handleEmailSettingChange('weeklyReports', e.target.checked)}
                  />
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* SMS Notifications */}
      <div className="bg-card rounded-lg border border-border p-6">
        <div className="flex items-center space-x-3 mb-6">
          <div className="w-10 h-10 bg-accent/10 rounded-lg flex items-center justify-center">
            <Icon name="MessageSquare" size={20} color="var(--color-accent)" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-foreground">SMS Notifications</h3>
            <p className="text-sm text-muted-foreground">Configure SMS alert preferences</p>
          </div>
        </div>

        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <Checkbox
              label="Enable SMS Notifications"
              description="Receive notifications via SMS"
              checked={smsSettings.enabled}
              onChange={(e) => handleSmsSettingChange('enabled', e.target.checked)}
            />
            <Button
              variant="outline"
              size="sm"
              onClick={() => sendTestNotification('sms')}
              disabled={!smsSettings.enabled || testNotifications.sms}
              loading={testNotifications.sms}
              iconName="Send"
              iconPosition="left"
            >
              Test SMS
            </Button>
          </div>

          {smsSettings.enabled && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="space-y-4">
                <Input
                  label="Phone Number"
                  type="tel"
                  value={smsSettings.phoneNumber}
                  onChange={(e) => handleSmsSettingChange('phoneNumber', e.target.value)}
                  placeholder="+91 98765 43210"
                />
                <Checkbox
                  label="Emergency Only"
                  description="Only send SMS for critical alerts"
                  checked={smsSettings.emergencyOnly}
                  onChange={(e) => handleSmsSettingChange('emergencyOnly', e.target.checked)}
                />
              </div>

              <div className="space-y-4">
                <div className="space-y-3">
                  <h4 className="text-sm font-medium text-foreground">SMS Preferences</h4>
                  <Checkbox
                    label="Signal Generation"
                    description="New buy/sell signals"
                    checked={smsSettings.signalGeneration}
                    onChange={(e) => handleSmsSettingChange('signalGeneration', e.target.checked)}
                    disabled={smsSettings.emergencyOnly}
                  />
                  <Checkbox
                    label="Order Execution"
                    description="Order confirmations"
                    checked={smsSettings.orderExecution}
                    onChange={(e) => handleSmsSettingChange('orderExecution', e.target.checked)}
                  />
                  <Checkbox
                    label="System Alerts"
                    description="Critical system errors"
                    checked={smsSettings.systemAlerts}
                    onChange={(e) => handleSmsSettingChange('systemAlerts', e.target.checked)}
                  />
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Push Notifications */}
      <div className="bg-card rounded-lg border border-border p-6">
        <div className="flex items-center space-x-3 mb-6">
          <div className="w-10 h-10 bg-success/10 rounded-lg flex items-center justify-center">
            <Icon name="Bell" size={20} color="var(--color-success)" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-foreground">Push Notifications</h3>
            <p className="text-sm text-muted-foreground">Configure browser push notifications</p>
          </div>
        </div>

        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <Checkbox
              label="Enable Push Notifications"
              description="Receive notifications in browser"
              checked={pushSettings.enabled}
              onChange={(e) => handlePushSettingChange('enabled', e.target.checked)}
            />
            <Button
              variant="outline"
              size="sm"
              onClick={() => sendTestNotification('push')}
              disabled={!pushSettings.enabled || testNotifications.push}
              loading={testNotifications.push}
              iconName="Send"
              iconPosition="left"
            >
              Test Push
            </Button>
          </div>

          {pushSettings.enabled && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="space-y-3">
                <h4 className="text-sm font-medium text-foreground">Trading Notifications</h4>
                <Checkbox
                  label="Signal Generation"
                  description="New buy/sell signals"
                  checked={pushSettings.signalGeneration}
                  onChange={(e) => handlePushSettingChange('signalGeneration', e.target.checked)}
                />
                <Checkbox
                  label="Order Execution"
                  description="Order confirmations"
                  checked={pushSettings.orderExecution}
                  onChange={(e) => handlePushSettingChange('orderExecution', e.target.checked)}
                />
                <Checkbox
                  label="System Alerts"
                  description="System status updates"
                  checked={pushSettings.systemAlerts}
                  onChange={(e) => handlePushSettingChange('systemAlerts', e.target.checked)}
                />
              </div>

              <div className="space-y-3">
                <h4 className="text-sm font-medium text-foreground">Market Notifications</h4>
                <Checkbox
                  label="Price Alerts"
                  description="Significant price movements"
                  checked={pushSettings.priceAlerts}
                  onChange={(e) => handlePushSettingChange('priceAlerts', e.target.checked)}
                />
                <Checkbox
                  label="News Alerts"
                  description="Market news and updates"
                  checked={pushSettings.newsAlerts}
                  onChange={(e) => handlePushSettingChange('newsAlerts', e.target.checked)}
                />
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Alert Thresholds */}
      <div className="bg-card rounded-lg border border-border p-6">
        <div className="flex items-center space-x-3 mb-6">
          <div className="w-10 h-10 bg-warning/10 rounded-lg flex items-center justify-center">
            <Icon name="AlertTriangle" size={20} color="var(--color-warning)" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-foreground">Alert Thresholds</h3>
            <p className="text-sm text-muted-foreground">Configure when to trigger notifications</p>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="space-y-4">
            <Input
              label="Profit Threshold (₹)"
              type="number"
              description="Alert when profit exceeds this amount"
              value={alertThresholds.profitThreshold}
              onChange={(e) => handleThresholdChange('profitThreshold', parseInt(e.target.value))}
            />

            <Input
              label="Loss Threshold (₹)"
              type="number"
              description="Alert when loss exceeds this amount"
              value={alertThresholds.lossThreshold}
              onChange={(e) => handleThresholdChange('lossThreshold', parseInt(e.target.value))}
            />

            <Input
              label="Volume Threshold"
              type="number"
              description="Alert when volume drops below this level"
              value={alertThresholds.volumeThreshold}
              onChange={(e) => handleThresholdChange('volumeThreshold', parseInt(e.target.value))}
            />
          </div>

          <div className="space-y-4">
            <Input
              label="Price Change Threshold (%)"
              type="number"
              description="Alert when price changes by this percentage"
              value={alertThresholds.priceChangeThreshold}
              onChange={(e) => handleThresholdChange('priceChangeThreshold', parseFloat(e.target.value))}
              step="0.1"
            />

            <Input
              label="Error Rate Threshold (%)"
              type="number"
              description="Alert when error rate exceeds this percentage"
              value={alertThresholds.errorRateThreshold}
              onChange={(e) => handleThresholdChange('errorRateThreshold', parseFloat(e.target.value))}
              step="0.1"
            />
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex flex-col sm:flex-row gap-4">
        <Button
          variant="default"
          onClick={saveNotificationSettings}
          iconName="Save"
          iconPosition="left"
          className="flex-1 sm:flex-none"
        >
          Save Notification Settings
        </Button>
        <Button
          variant="outline"
          onClick={resetNotificationSettings}
          iconName="RotateCcw"
          iconPosition="left"
          className="flex-1 sm:flex-none"
        >
          Reset to Defaults
        </Button>
      </div>
    </div>
  );
};

export default NotificationSettings;