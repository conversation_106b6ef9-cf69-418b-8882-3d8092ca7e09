import React, { useState, useEffect, useMemo } from 'react';
import { Helmet } from 'react-helmet';
import Header from '../../components/ui/Header';
import Sidebar from '../../components/ui/Sidebar';
import TradeFilters from './components/TradeFilters';
import TradeTable from './components/TradeTable';
import TradeSummary from './components/TradeSummary';
import PerformanceCharts from './components/PerformanceCharts';

const TradeLog = () => {
  const [filters, setFilters] = useState({
    dateRange: 'all',
    symbol: '',
    tradeType: 'all',
    profitLoss: 'all',
    startDate: '',
    endDate: '',
    search: ''
  });

  const [sortConfig, setSortConfig] = useState({
    key: 'buyDate',
    direction: 'desc'
  });

  // Mock trade data
  const mockTrades = [
    {
      id: 1,
      symbol: 'RELIANCE',
      type: 'BUY',
      buyDate: '2024-01-15T09:30:00',
      sellDate: '2024-01-22T15:15:00',
      quantity: 50,
      buyPrice: 2456.75,
      sellPrice: 2598.20,
      pnl: 7072.50,
      pnlPercent: 5.76,
      currentPrice: 2598.20,
      gttOrderId: 'GTT001',
      triggerPrice: 2456.75,
      targetPrice: 2603.15,
      status: 'COMPLETED',
      brokerage: 45.50,
      ohlc: {
        open: 2450.00,
        high: 2610.50,
        low: 2445.25,
        close: 2598.20
      }
    },
    {
      id: 2,
      symbol: 'TCS',
      type: 'BUY',
      buyDate: '2024-01-18T10:15:00',
      sellDate: '2024-01-25T14:30:00',
      quantity: 30,
      buyPrice: 3234.50,
      sellPrice: 3128.75,
      pnl: -3172.50,
      pnlPercent: -3.27,
      currentPrice: 3128.75,
      gttOrderId: 'GTT002',
      triggerPrice: 3234.50,
      targetPrice: 3428.57,
      status: 'COMPLETED',
      brokerage: 38.75,
      ohlc: {
        open: 3240.00,
        high: 3245.80,
        low: 3120.00,
        close: 3128.75
      }
    },
    {
      id: 3,
      symbol: 'INFY',
      type: 'BUY',
      buyDate: '2024-01-20T11:45:00',
      sellDate: '2024-02-02T13:20:00',
      quantity: 75,
      buyPrice: 1567.25,
      sellPrice: 1681.30,
      pnl: 8553.75,
      pnlPercent: 7.28,
      currentPrice: 1681.30,
      gttOrderId: 'GTT003',
      triggerPrice: 1567.25,
      targetPrice: 1661.29,
      status: 'COMPLETED',
      brokerage: 52.25,
      ohlc: {
        open: 1570.00,
        high: 1685.50,
        low: 1562.75,
        close: 1681.30
      }
    },
    {
      id: 4,
      symbol: 'HDFC',
      type: 'BUY',
      buyDate: '2024-01-25T09:45:00',
      sellDate: null,
      quantity: 40,
      buyPrice: 1654.80,
      sellPrice: null,
      pnl: 1587.20,
      pnlPercent: 2.40,
      currentPrice: 1694.48,
      gttOrderId: 'GTT004',
      triggerPrice: 1654.80,
      targetPrice: 1754.09,
      status: 'ACTIVE',
      brokerage: 0,
      ohlc: {
        open: 1650.00,
        high: 1698.75,
        low: 1648.25,
        close: 1694.48
      }
    },
    {
      id: 5,
      symbol: 'ICICIBANK',
      type: 'BUY',
      buyDate: '2024-02-01T10:30:00',
      sellDate: '2024-02-08T15:00:00',
      quantity: 60,
      buyPrice: 987.50,
      sellPrice: 1045.25,
      pnl: 3465.00,
      pnlPercent: 5.85,
      currentPrice: 1045.25,
      gttOrderId: 'GTT005',
      triggerPrice: 987.50,
      targetPrice: 1046.75,
      status: 'COMPLETED',
      brokerage: 42.00,
      ohlc: {
        open: 985.00,
        high: 1048.50,
        low: 982.75,
        close: 1045.25
      }
    },
    {
      id: 6,
      symbol: 'WIPRO',
      type: 'BUY',
      buyDate: '2024-02-05T11:15:00',
      sellDate: '2024-02-12T14:45:00',
      quantity: 100,
      buyPrice: 456.75,
      sellPrice: 432.50,
      pnl: -2425.00,
      pnlPercent: -5.31,
      currentPrice: 432.50,
      gttOrderId: 'GTT006',
      triggerPrice: 456.75,
      targetPrice: 484.16,
      status: 'COMPLETED',
      brokerage: 35.50,
      ohlc: {
        open: 458.00,
        high: 460.25,
        low: 430.00,
        close: 432.50
      }
    },
    {
      id: 7,
      symbol: 'BAJFINANCE',
      type: 'BUY',
      buyDate: '2024-02-10T09:30:00',
      sellDate: null,
      quantity: 25,
      buyPrice: 6789.50,
      sellPrice: null,
      pnl: -1697.50,
      pnlPercent: -1.00,
      currentPrice: 6721.60,
      gttOrderId: 'GTT007',
      triggerPrice: 6789.50,
      targetPrice: 7196.87,
      status: 'ACTIVE',
      brokerage: 0,
      ohlc: {
        open: 6785.00,
        high: 6795.25,
        low: 6715.50,
        close: 6721.60
      }
    },
    {
      id: 8,
      symbol: 'MARUTI',
      type: 'BUY',
      buyDate: '2024-02-15T10:45:00',
      sellDate: '2024-02-22T13:30:00',
      quantity: 20,
      buyPrice: 10234.75,
      sellPrice: 10849.25,
      pnl: 12290.00,
      pnlPercent: 6.01,
      currentPrice: 10849.25,
      gttOrderId: 'GTT008',
      triggerPrice: 10234.75,
      targetPrice: 10848.84,
      status: 'COMPLETED',
      brokerage: 78.50,
      ohlc: {
        open: 10240.00,
        high: 10865.50,
        low: 10225.75,
        close: 10849.25
      }
    },
    {
      id: 9,
      symbol: 'ASIANPAINT',
      type: 'BUY',
      buyDate: '2024-02-20T11:00:00',
      sellDate: null,
      quantity: 35,
      buyPrice: 3456.25,
      sellPrice: null,
      pnl: 2450.25,
      pnlPercent: 2.03,
      currentPrice: 3526.25,
      gttOrderId: 'GTT009',
      triggerPrice: 3456.25,
      targetPrice: 3663.63,
      status: 'ACTIVE',
      brokerage: 0,
      ohlc: {
        open: 3450.00,
        high: 3535.75,
        low: 3445.50,
        close: 3526.25
      }
    },
    {
      id: 10,
      symbol: 'HCLTECH',
      type: 'BUY',
      buyDate: '2024-02-25T09:15:00',
      sellDate: '2024-03-05T15:30:00',
      quantity: 80,
      buyPrice: 1234.50,
      sellPrice: 1308.57,
      pnl: 5925.60,
      pnlPercent: 6.00,
      currentPrice: 1308.57,
      gttOrderId: 'GTT010',
      triggerPrice: 1234.50,
      targetPrice: 1308.57,
      status: 'COMPLETED',
      brokerage: 48.75,
      ohlc: {
        open: 1230.00,
        high: 1315.25,
        low: 1228.75,
        close: 1308.57
      }
    }
  ];

  // Filter trades based on current filters
  const filteredTrades = useMemo(() => {
    let filtered = [...mockTrades];

    // Date range filter
    if (filters.dateRange !== 'all') {
      const now = new Date();
      let startDate = new Date();

      switch (filters.dateRange) {
        case 'today':
          startDate.setHours(0, 0, 0, 0);
          break;
        case 'week':
          startDate.setDate(now.getDate() - 7);
          break;
        case 'month':
          startDate.setMonth(now.getMonth() - 1);
          break;
        case 'quarter':
          startDate.setMonth(now.getMonth() - 3);
          break;
        case 'year':
          startDate.setFullYear(now.getFullYear() - 1);
          break;
        case 'custom':
          if (filters.startDate && filters.endDate) {
            startDate = new Date(filters.startDate);
            const endDate = new Date(filters.endDate);
            filtered = filtered.filter(trade => {
              const tradeDate = new Date(trade.buyDate);
              return tradeDate >= startDate && tradeDate <= endDate;
            });
          }
          break;
        default:
          break;
      }

      if (filters.dateRange !== 'custom') {
        filtered = filtered.filter(trade => {
          const tradeDate = new Date(trade.buyDate);
          return tradeDate >= startDate;
        });
      }
    }

    // Symbol filter
    if (filters.symbol) {
      filtered = filtered.filter(trade => 
        trade.symbol.toLowerCase().includes(filters.symbol.toLowerCase())
      );
    }

    // Trade type filter
    if (filters.tradeType !== 'all') {
      filtered = filtered.filter(trade => 
        trade.type.toLowerCase() === filters.tradeType.toLowerCase()
      );
    }

    // Profit/Loss filter
    if (filters.profitLoss !== 'all') {
      filtered = filtered.filter(trade => {
        if (filters.profitLoss === 'profit') return trade.pnl > 0;
        if (filters.profitLoss === 'loss') return trade.pnl < 0;
        if (filters.profitLoss === 'breakeven') return trade.pnl === 0;
        return true;
      });
    }

    // Search filter
    if (filters.search) {
      const searchTerm = filters.search.toLowerCase();
      filtered = filtered.filter(trade => 
        trade.symbol.toLowerCase().includes(searchTerm) ||
        trade.gttOrderId.toLowerCase().includes(searchTerm)
      );
    }

    return filtered;
  }, [filters]);

  // Sort trades
  const sortedTrades = useMemo(() => {
    if (!sortConfig.key) return filteredTrades;

    return [...filteredTrades].sort((a, b) => {
      let aValue = a[sortConfig.key];
      let bValue = b[sortConfig.key];

      // Handle date sorting
      if (sortConfig.key.includes('Date')) {
        aValue = new Date(aValue || 0);
        bValue = new Date(bValue || 0);
      }

      // Handle null values
      if (aValue === null && bValue === null) return 0;
      if (aValue === null) return sortConfig.direction === 'asc' ? 1 : -1;
      if (bValue === null) return sortConfig.direction === 'asc' ? -1 : 1;

      if (aValue < bValue) {
        return sortConfig.direction === 'asc' ? -1 : 1;
      }
      if (aValue > bValue) {
        return sortConfig.direction === 'asc' ? 1 : -1;
      }
      return 0;
    });
  }, [filteredTrades, sortConfig]);

  const handleSort = (key) => {
    setSortConfig(prevConfig => ({
      key,
      direction: prevConfig.key === key && prevConfig.direction === 'asc' ? 'desc' : 'asc'
    }));
  };

  const handleFiltersChange = (newFilters) => {
    setFilters(newFilters);
  };

  const handleExport = () => {
    const csvHeaders = [
      'Symbol', 'Type', 'Buy Date', 'Sell Date', 'Quantity', 
      'Buy Price', 'Sell Price', 'P&L', 'P&L %', 'GTT Order ID', 'Status'
    ];

    const csvData = sortedTrades.map(trade => [
      trade.symbol,
      trade.type,
      new Date(trade.buyDate).toLocaleDateString('en-IN'),
      trade.sellDate ? new Date(trade.sellDate).toLocaleDateString('en-IN') : '',
      trade.quantity,
      trade.buyPrice,
      trade.sellPrice || '',
      trade.pnl,
      trade.pnlPercent,
      trade.gttOrderId,
      trade.status
    ]);

    const csvContent = [csvHeaders, ...csvData]
      .map(row => row.map(field => `"${field}"`).join(','))
      .join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `trade-log-${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleReset = () => {
    setSortConfig({ key: 'buyDate', direction: 'desc' });
  };

  return (
    <>
      <Helmet>
        <title>Trade Log - Darvas Trading Dashboard</title>
        <meta name="description" content="Comprehensive trade history and performance analysis for Darvas Box trading strategy" />
      </Helmet>

      <div className="min-h-screen bg-background">
        <Header />
        <Sidebar />
        
        <main className="pt-16 lg:ml-80">
          <div className="p-6 space-y-6">
            {/* Page Header */}
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold text-foreground">Trade Log</h1>
                <p className="text-muted-foreground">
                  Comprehensive transaction history and performance analysis
                </p>
              </div>
              <div className="text-sm text-muted-foreground">
                Last updated: {new Date().toLocaleString('en-IN')}
              </div>
            </div>

            {/* Trade Summary */}
            <TradeSummary trades={sortedTrades} />

            {/* Performance Charts */}
            <PerformanceCharts trades={sortedTrades} />

            {/* Trade Filters */}
            <TradeFilters
              onFiltersChange={handleFiltersChange}
              onExport={handleExport}
              onReset={handleReset}
            />

            {/* Trade Table */}
            <TradeTable
              trades={sortedTrades}
              onSort={handleSort}
              sortConfig={sortConfig}
            />
          </div>
        </main>
      </div>
    </>
  );
};

export default TradeLog;