import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const StrategyToggle = ({ isActive, onToggle, lastSignalTime, nextSignalTime }) => {
  const [isLoading, setIsLoading] = useState(false);

  const handleToggle = async () => {
    setIsLoading(true);
    try {
      await onToggle(!isActive);
    } finally {
      setIsLoading(false);
    }
  };

  const formatDateTime = (date) => {
    return date.toLocaleString('en-IN', {
      weekday: 'short',
      day: '2-digit',
      month: 'short',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="bg-card border border-border rounded-lg p-6 shadow-trading">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-lg font-semibold text-foreground mb-1">
            Darvas Strategy
          </h3>
          <p className="text-sm text-muted-foreground">
            Automated breakout trading based on 52-week highs
          </p>
        </div>
        
        <div className="flex items-center space-x-3">
          <div className={`flex items-center space-x-2 px-3 py-1 rounded-full text-sm font-medium ${
            isActive 
              ? 'bg-success/10 text-success' :'bg-muted text-muted-foreground'
          }`}>
            <div className={`w-2 h-2 rounded-full ${
              isActive ? 'bg-success animate-pulse-trading' : 'bg-muted-foreground'
            }`} />
            <span>{isActive ? 'Active' : 'Paused'}</span>
          </div>
          
          <Button
            variant={isActive ? "destructive" : "default"}
            onClick={handleToggle}
            loading={isLoading}
            iconName={isActive ? "Pause" : "Play"}
            iconPosition="left"
          >
            {isActive ? 'Pause Strategy' : 'Activate Strategy'}
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="bg-muted/50 rounded-lg p-4">
          <div className="flex items-center space-x-2 mb-2">
            <Icon name="Clock" size={16} color="var(--color-muted-foreground)" />
            <span className="text-sm font-medium text-muted-foreground">Last Signal</span>
          </div>
          <div className="text-sm font-data text-foreground">
            {lastSignalTime ? formatDateTime(lastSignalTime) : 'No signals yet'}
          </div>
        </div>

        <div className="bg-muted/50 rounded-lg p-4">
          <div className="flex items-center space-x-2 mb-2">
            <Icon name="Calendar" size={16} color="var(--color-muted-foreground)" />
            <span className="text-sm font-medium text-muted-foreground">Next Signal</span>
          </div>
          <div className="text-sm font-data text-foreground">
            {nextSignalTime ? formatDateTime(nextSignalTime) : 'Friday 8:00 PM'}
          </div>
        </div>
      </div>

      <div className="mt-4 p-4 bg-accent/5 border border-accent/20 rounded-lg">
        <div className="flex items-start space-x-3">
          <Icon name="Info" size={16} color="var(--color-accent)" className="mt-0.5" />
          <div className="text-sm text-foreground">
            <p className="font-medium mb-1">Strategy Rules:</p>
            <ul className="text-muted-foreground space-y-1">
              <li>• Max ₹10,000 per stock, ₹2,000 per entry</li>
              <li>• 5 entries maximum per stock</li>
              <li>• Target: 6% above average price</li>
              <li>• Auto-pause when capital exhausted</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default StrategyToggle;