@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Core Colors */
    --color-background: #FAFAFA; /* warm off-white */
    --color-foreground: #1E293B; /* rich dark gray */
    --color-border: #E2E8F0; /* light gray */
    --color-input: #FFFFFF; /* pure white */
    --color-ring: #2563EB; /* deep blue */
    
    /* Card Colors */
    --color-card: #FFFFFF; /* pure white */
    --color-card-foreground: #1E293B; /* rich dark gray */
    
    /* Popover Colors */
    --color-popover: #FFFFFF; /* pure white */
    --color-popover-foreground: #1E293B; /* rich dark gray */
    
    /* Muted Colors */
    --color-muted: #F8FAFC; /* very light gray */
    --color-muted-foreground: #64748B; /* medium gray */
    
    /* Primary Colors */
    --color-primary: #2563EB; /* deep blue */
    --color-primary-foreground: #FFFFFF; /* white */
    
    /* Secondary Colors */
    --color-secondary: #64748B; /* professional slate gray */
    --color-secondary-foreground: #FFFFFF; /* white */
    
    /* Accent Colors */
    --color-accent: #0EA5E9; /* lighter blue */
    --color-accent-foreground: #FFFFFF; /* white */
    
    /* Success Colors */
    --color-success: #059669; /* forest green */
    --color-success-foreground: #FFFFFF; /* white */
    
    /* Warning Colors */
    --color-warning: #D97706; /* amber orange */
    --color-warning-foreground: #FFFFFF; /* white */
    
    /* Error/Destructive Colors */
    --color-error: #DC2626; /* professional red */
    --color-error-foreground: #FFFFFF; /* white */
    --color-destructive: #DC2626; /* professional red */
    --color-destructive-foreground: #FFFFFF; /* white */
  }
  
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer utilities {
  .font-data {
    font-family: 'JetBrains Mono', monospace;
  }
  
  .shadow-trading {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }
  
  .shadow-trading-lg {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  }
  
  .animate-pulse-trading {
    animation: pulse-trading 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }
  
  @keyframes pulse-trading {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: .7;
    }
  }
}