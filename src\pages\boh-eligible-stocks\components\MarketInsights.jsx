import React from 'react';
import Icon from '../../../components/AppIcon';
import { PieChart, Pie, Cell, ResponsiveContainer, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip } from 'recharts';

const MarketInsights = ({ sectorData, trendData }) => {
  const COLORS = ['#2563EB', '#059669', '#D97706', '#DC2626', '#7C3AED', '#0EA5E9', '#EC4899', '#10B981'];

  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-popover border border-border rounded-lg shadow-trading-lg p-3">
          <p className="text-sm font-medium text-foreground">{label}</p>
          <p className="text-sm text-muted-foreground">
            {payload[0].name}: <span className="font-data font-medium">{payload[0].value}</span>
          </p>
        </div>
      );
    }
    return null;
  };

  const formatPercentage = (value) => `${value.toFixed(1)}%`;

  return (
    <div className="space-y-6">
      {/* Market Trend Indicator */}
      <div className="bg-card rounded-lg border border-border p-6">
        <div className="flex items-center space-x-3 mb-4">
          <div className="w-8 h-8 bg-accent/10 rounded-lg flex items-center justify-center">
            <Icon name="TrendingUp" size={16} color="var(--color-accent)" />
          </div>
          <h3 className="text-lg font-semibold text-foreground">Market Trend</h3>
        </div>
        
        <div className="grid grid-cols-1 gap-4">
          <div className="bg-muted/30 rounded-lg p-4">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm text-muted-foreground">Overall Market Sentiment</span>
              <div className="flex items-center space-x-1">
                <Icon name="TrendingUp" size={14} color="var(--color-success)" />
                <span className="text-sm font-medium text-success">Bullish</span>
              </div>
            </div>
            <p className="text-xs text-muted-foreground">
              23 stocks at 52-week highs indicate strong upward momentum
            </p>
          </div>
          
          <div className="bg-muted/30 rounded-lg p-4">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm text-muted-foreground">Breakout Strength</span>
              <span className="text-sm font-data font-medium text-foreground">High</span>
            </div>
            <div className="w-full bg-background rounded-full h-2">
              <div className="bg-success h-2 rounded-full" style={{ width: '78%' }} />
            </div>
          </div>
        </div>
      </div>

      {/* Sector Distribution */}
      <div className="bg-card rounded-lg border border-border p-6">
        <div className="flex items-center space-x-3 mb-4">
          <div className="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center">
            <Icon name="PieChart" size={16} color="var(--color-primary)" />
          </div>
          <h3 className="text-lg font-semibold text-foreground">Sector Breakouts</h3>
        </div>
        
        <div className="h-64 mb-4">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={sectorData}
                cx="50%"
                cy="50%"
                innerRadius={60}
                outerRadius={100}
                paddingAngle={2}
                dataKey="count"
              >
                {sectorData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Pie>
              <Tooltip content={<CustomTooltip />} />
            </PieChart>
          </ResponsiveContainer>
        </div>
        
        <div className="grid grid-cols-2 gap-2">
          {sectorData.map((sector, index) => (
            <div key={sector.name} className="flex items-center space-x-2">
              <div 
                className="w-3 h-3 rounded-full" 
                style={{ backgroundColor: COLORS[index % COLORS.length] }}
              />
              <span className="text-xs text-muted-foreground">{sector.name}</span>
              <span className="text-xs font-data font-medium text-foreground ml-auto">
                {sector.count}
              </span>
            </div>
          ))}
        </div>
      </div>

      {/* Weekly Trend */}
      <div className="bg-card rounded-lg border border-border p-6">
        <div className="flex items-center space-x-3 mb-4">
          <div className="w-8 h-8 bg-success/10 rounded-lg flex items-center justify-center">
            <Icon name="BarChart3" size={16} color="var(--color-success)" />
          </div>
          <h3 className="text-lg font-semibold text-foreground">Weekly Breakout Trend</h3>
        </div>
        
        <div className="h-48">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={trendData}>
              <CartesianGrid strokeDasharray="3 3" stroke="var(--color-border)" />
              <XAxis 
                dataKey="week" 
                tick={{ fontSize: 12, fill: 'var(--color-muted-foreground)' }}
                axisLine={{ stroke: 'var(--color-border)' }}
              />
              <YAxis 
                tick={{ fontSize: 12, fill: 'var(--color-muted-foreground)' }}
                axisLine={{ stroke: 'var(--color-border)' }}
              />
              <Tooltip content={<CustomTooltip />} />
              <Bar dataKey="breakouts" fill="var(--color-success)" radius={[2, 2, 0, 0]} />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="bg-card rounded-lg border border-border p-6">
        <div className="flex items-center space-x-3 mb-4">
          <div className="w-8 h-8 bg-warning/10 rounded-lg flex items-center justify-center">
            <Icon name="Target" size={16} color="var(--color-warning)" />
          </div>
          <h3 className="text-lg font-semibold text-foreground">Key Metrics</h3>
        </div>
        
        <div className="grid grid-cols-1 gap-4">
          <div className="flex items-center justify-between p-3 bg-muted/30 rounded-lg">
            <div className="flex items-center space-x-2">
              <Icon name="Zap" size={16} color="var(--color-accent)" />
              <span className="text-sm text-muted-foreground">Average Breakout Strength</span>
            </div>
            <span className="text-sm font-data font-medium text-foreground">+2.8%</span>
          </div>
          
          <div className="flex items-center justify-between p-3 bg-muted/30 rounded-lg">
            <div className="flex items-center space-x-2">
              <Icon name="Clock" size={16} color="var(--color-warning)" />
              <span className="text-sm text-muted-foreground">Avg. Time at High</span>
            </div>
            <span className="text-sm font-data font-medium text-foreground">3.2 days</span>
          </div>
          
          <div className="flex items-center justify-between p-3 bg-muted/30 rounded-lg">
            <div className="flex items-center space-x-2">
              <Icon name="TrendingUp" size={16} color="var(--color-success)" />
              <span className="text-sm text-muted-foreground">Success Rate (Last 30 days)</span>
            </div>
            <span className="text-sm font-data font-medium text-success">68.4%</span>
          </div>
        </div>
      </div>

      {/* Market Alerts */}
      <div className="bg-card rounded-lg border border-border p-6">
        <div className="flex items-center space-x-3 mb-4">
          <div className="w-8 h-8 bg-error/10 rounded-lg flex items-center justify-center">
            <Icon name="AlertTriangle" size={16} color="var(--color-error)" />
          </div>
          <h3 className="text-lg font-semibold text-foreground">Market Alerts</h3>
        </div>
        
        <div className="space-y-3">
          <div className="flex items-start space-x-3 p-3 bg-warning/10 rounded-lg">
            <Icon name="Info" size={16} color="var(--color-warning)" className="mt-0.5" />
            <div>
              <p className="text-sm font-medium text-foreground">High Volume Breakouts</p>
              <p className="text-xs text-muted-foreground">
                5 stocks showing breakouts with 2x average volume
              </p>
            </div>
          </div>
          
          <div className="flex items-start space-x-3 p-3 bg-accent/10 rounded-lg">
            <Icon name="TrendingUp" size={16} color="var(--color-accent)" className="mt-0.5" />
            <div>
              <p className="text-sm font-medium text-foreground">Sector Momentum</p>
              <p className="text-xs text-muted-foreground">
                IT sector showing strong breakout momentum (8 stocks)
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MarketInsights;