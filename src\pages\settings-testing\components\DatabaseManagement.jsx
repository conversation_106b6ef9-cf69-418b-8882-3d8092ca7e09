import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';
import Input from '../../../components/ui/Input';
import Select from '../../../components/ui/Select';
import { Checkbox } from '../../../components/ui/Checkbox';

const DatabaseManagement = () => {
  const [databaseStats, setDatabaseStats] = useState({
    totalRecords: 15847,
    tradeRecords: 1247,
    signalRecords: 3456,
    orderRecords: 2134,
    databaseSize: '45.2 MB',
    lastBackup: new Date(Date.now() - 86400000),
    backupSize: '42.8 MB'
  });

  const [backupSettings, setBackupSettings] = useState({
    autoBackup: true,
    backupFrequency: 'daily',
    retentionDays: 30,
    includeTestData: false,
    compressBackup: true
  });

  const [cleanupSettings, setCleanupSettings] = useState({
    deleteTestData: false,
    deleteOldTrades: false,
    deleteOldSignals: false,
    retentionDays: 90,
    confirmDeletion: true
  });

  const [importExportSettings, setImportExportSettings] = useState({
    exportFormat: 'json',
    includeMetadata: true,
    dateRange: 'all'
  });

  const [operationStatus, setOperationStatus] = useState({
    backup: { running: false, progress: 0, lastRun: null },
    cleanup: { running: false, progress: 0, lastRun: null },
    export: { running: false, progress: 0, lastRun: null },
    import: { running: false, progress: 0, lastRun: null }
  });

  const backupFrequencyOptions = [
    { value: 'hourly', label: 'Every Hour' },
    { value: 'daily', label: 'Daily' },
    { value: 'weekly', label: 'Weekly' },
    { value: 'monthly', label: 'Monthly' }
  ];

  const exportFormatOptions = [
    { value: 'json', label: 'JSON Format' },
    { value: 'csv', label: 'CSV Format' },
    { value: 'xlsx', label: 'Excel Format' }
  ];

  const dateRangeOptions = [
    { value: 'all', label: 'All Data' },
    { value: 'last_month', label: 'Last Month' },
    { value: 'last_3_months', label: 'Last 3 Months' },
    { value: 'last_year', label: 'Last Year' },
    { value: 'custom', label: 'Custom Range' }
  ];

  const handleBackupSettingChange = (key, value) => {
    setBackupSettings(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleCleanupSettingChange = (key, value) => {
    setCleanupSettings(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleImportExportSettingChange = (key, value) => {
    setImportExportSettings(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const simulateOperation = async (operationType, duration = 3000) => {
    setOperationStatus(prev => ({
      ...prev,
      [operationType]: { ...prev[operationType], running: true, progress: 0 }
    }));

    const steps = 20;
    const stepDuration = duration / steps;

    for (let i = 1; i <= steps; i++) {
      await new Promise(resolve => setTimeout(resolve, stepDuration));
      setOperationStatus(prev => ({
        ...prev,
        [operationType]: { ...prev[operationType], progress: (i / steps) * 100 }
      }));
    }

    setOperationStatus(prev => ({
      ...prev,
      [operationType]: { running: false, progress: 100, lastRun: new Date() }
    }));

    setTimeout(() => {
      setOperationStatus(prev => ({
        ...prev,
        [operationType]: { ...prev[operationType], progress: 0 }
      }));
    }, 2000);
  };

  const createBackup = () => {
    console.log('Creating backup with settings:', backupSettings);
    simulateOperation('backup');
  };

  const restoreBackup = () => {
    console.log('Restoring backup...');
    simulateOperation('import', 5000);
  };

  const cleanupDatabase = () => {
    if (cleanupSettings.confirmDeletion) {
      const confirmed = window.confirm('Are you sure you want to cleanup the database? This action cannot be undone.');
      if (!confirmed) return;
    }
    console.log('Cleaning up database with settings:', cleanupSettings);
    simulateOperation('cleanup', 4000);
  };

  const exportData = () => {
    console.log('Exporting data with settings:', importExportSettings);
    simulateOperation('export', 2000);
  };

  const importData = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json,.csv,.xlsx';
    input.onchange = (e) => {
      const file = e.target.files[0];
      if (file) {
        console.log('Importing file:', file.name);
        simulateOperation('import', 4000);
      }
    };
    input.click();
  };

  const formatFileSize = (bytes) => {
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 Bytes';
    let i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  return (
    <div className="space-y-8">
      {/* Database Overview */}
      <div className="bg-card rounded-lg border border-border p-6">
        <div className="flex items-center space-x-3 mb-6">
          <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
            <Icon name="Database" size={20} color="var(--color-primary)" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-foreground">Database Overview</h3>
            <p className="text-sm text-muted-foreground">Current database statistics and information</p>
          </div>
        </div>

        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="bg-muted/50 rounded-lg p-4">
            <div className="flex items-center space-x-2 mb-2">
              <Icon name="FileText" size={16} className="text-muted-foreground" />
              <span className="text-sm text-muted-foreground">Total Records</span>
            </div>
            <div className="text-lg font-data font-semibold">{databaseStats.totalRecords.toLocaleString()}</div>
          </div>

          <div className="bg-muted/50 rounded-lg p-4">
            <div className="flex items-center space-x-2 mb-2">
              <Icon name="TrendingUp" size={16} className="text-muted-foreground" />
              <span className="text-sm text-muted-foreground">Trade Records</span>
            </div>
            <div className="text-lg font-data font-semibold">{databaseStats.tradeRecords.toLocaleString()}</div>
          </div>

          <div className="bg-muted/50 rounded-lg p-4">
            <div className="flex items-center space-x-2 mb-2">
              <Icon name="Zap" size={16} className="text-muted-foreground" />
              <span className="text-sm text-muted-foreground">Signal Records</span>
            </div>
            <div className="text-lg font-data font-semibold">{databaseStats.signalRecords.toLocaleString()}</div>
          </div>

          <div className="bg-muted/50 rounded-lg p-4">
            <div className="flex items-center space-x-2 mb-2">
              <Icon name="ShoppingCart" size={16} className="text-muted-foreground" />
              <span className="text-sm text-muted-foreground">Order Records</span>
            </div>
            <div className="text-lg font-data font-semibold">{databaseStats.orderRecords.toLocaleString()}</div>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
          <div className="bg-muted/50 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground">Database Size</span>
              <span className="text-sm font-data font-medium">{databaseStats.databaseSize}</span>
            </div>
          </div>

          <div className="bg-muted/50 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground">Last Backup</span>
              <span className="text-sm font-data font-medium">
                {databaseStats.lastBackup.toLocaleDateString('en-IN')}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Backup & Restore */}
      <div className="bg-card rounded-lg border border-border p-6">
        <div className="flex items-center space-x-3 mb-6">
          <div className="w-10 h-10 bg-success/10 rounded-lg flex items-center justify-center">
            <Icon name="Shield" size={20} color="var(--color-success)" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-foreground">Backup & Restore</h3>
            <p className="text-sm text-muted-foreground">Manage database backups and restoration</p>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="space-y-4">
            <h4 className="text-sm font-medium text-foreground">Backup Settings</h4>
            
            <Checkbox
              label="Automatic Backup"
              description="Enable scheduled automatic backups"
              checked={backupSettings.autoBackup}
              onChange={(e) => handleBackupSettingChange('autoBackup', e.target.checked)}
            />

            <Select
              label="Backup Frequency"
              options={backupFrequencyOptions}
              value={backupSettings.backupFrequency}
              onChange={(value) => handleBackupSettingChange('backupFrequency', value)}
              disabled={!backupSettings.autoBackup}
            />

            <Input
              label="Retention Days"
              type="number"
              description="Number of days to keep backups"
              value={backupSettings.retentionDays}
              onChange={(e) => handleBackupSettingChange('retentionDays', parseInt(e.target.value))}
              min="1"
              max="365"
            />

            <Checkbox
              label="Include Test Data"
              description="Include testing data in backups"
              checked={backupSettings.includeTestData}
              onChange={(e) => handleBackupSettingChange('includeTestData', e.target.checked)}
            />

            <Checkbox
              label="Compress Backup"
              description="Compress backup files to save space"
              checked={backupSettings.compressBackup}
              onChange={(e) => handleBackupSettingChange('compressBackup', e.target.checked)}
            />
          </div>

          <div className="space-y-4">
            <h4 className="text-sm font-medium text-foreground">Backup Operations</h4>
            
            <div className="space-y-3">
              <Button
                variant="default"
                onClick={createBackup}
                disabled={operationStatus.backup.running}
                loading={operationStatus.backup.running}
                iconName="Download"
                iconPosition="left"
                className="w-full"
              >
                Create Backup Now
              </Button>

              <Button
                variant="outline"
                onClick={restoreBackup}
                disabled={operationStatus.import.running}
                loading={operationStatus.import.running}
                iconName="Upload"
                iconPosition="left"
                className="w-full"
              >
                Restore from Backup
              </Button>

              {(operationStatus.backup.running || operationStatus.import.running) && (
                <div className="bg-muted/50 rounded-lg p-3">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm text-muted-foreground">
                      {operationStatus.backup.running ? 'Creating backup...' : 'Restoring backup...'}
                    </span>
                    <span className="text-sm font-data">
                      {Math.round(operationStatus.backup.running ? operationStatus.backup.progress : operationStatus.import.progress)}%
                    </span>
                  </div>
                  <div className="w-full bg-background rounded-full h-2">
                    <div 
                      className="h-2 bg-primary rounded-full transition-all duration-300"
                      style={{ 
                        width: `${operationStatus.backup.running ? operationStatus.backup.progress : operationStatus.import.progress}%` 
                      }}
                    />
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Data Cleanup */}
      <div className="bg-card rounded-lg border border-border p-6">
        <div className="flex items-center space-x-3 mb-6">
          <div className="w-10 h-10 bg-warning/10 rounded-lg flex items-center justify-center">
            <Icon name="Trash2" size={20} color="var(--color-warning)" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-foreground">Data Cleanup</h3>
            <p className="text-sm text-muted-foreground">Clean up old and test data to optimize database</p>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="space-y-4">
            <h4 className="text-sm font-medium text-foreground">Cleanup Options</h4>
            
            <Checkbox
              label="Delete Test Data"
              description="Remove all testing and simulation data"
              checked={cleanupSettings.deleteTestData}
              onChange={(e) => handleCleanupSettingChange('deleteTestData', e.target.checked)}
            />

            <Checkbox
              label="Delete Old Trades"
              description="Remove trade records older than retention period"
              checked={cleanupSettings.deleteOldTrades}
              onChange={(e) => handleCleanupSettingChange('deleteOldTrades', e.target.checked)}
            />

            <Checkbox
              label="Delete Old Signals"
              description="Remove signal records older than retention period"
              checked={cleanupSettings.deleteOldSignals}
              onChange={(e) => handleCleanupSettingChange('deleteOldSignals', e.target.checked)}
            />

            <Input
              label="Retention Days"
              type="number"
              description="Keep data newer than this many days"
              value={cleanupSettings.retentionDays}
              onChange={(e) => handleCleanupSettingChange('retentionDays', parseInt(e.target.value))}
              min="1"
              max="365"
            />

            <Checkbox
              label="Confirm Before Deletion"
              description="Show confirmation dialog before cleanup"
              checked={cleanupSettings.confirmDeletion}
              onChange={(e) => handleCleanupSettingChange('confirmDeletion', e.target.checked)}
            />
          </div>

          <div className="space-y-4">
            <h4 className="text-sm font-medium text-foreground">Cleanup Operations</h4>
            
            <div className="bg-warning/5 border border-warning/20 rounded-lg p-4 mb-4">
              <div className="flex items-start space-x-3">
                <Icon name="AlertTriangle" size={20} color="var(--color-warning)" />
                <div>
                  <h5 className="text-sm font-medium text-warning mb-1">Warning</h5>
                  <p className="text-xs text-muted-foreground">
                    Data cleanup operations are permanent and cannot be undone. 
                    Make sure to create a backup before proceeding.
                  </p>
                </div>
              </div>
            </div>

            <Button
              variant="warning"
              onClick={cleanupDatabase}
              disabled={operationStatus.cleanup.running || (!cleanupSettings.deleteTestData && !cleanupSettings.deleteOldTrades && !cleanupSettings.deleteOldSignals)}
              loading={operationStatus.cleanup.running}
              iconName="Trash2"
              iconPosition="left"
              className="w-full"
            >
              Start Cleanup
            </Button>

            {operationStatus.cleanup.running && (
              <div className="bg-muted/50 rounded-lg p-3">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm text-muted-foreground">Cleaning up database...</span>
                  <span className="text-sm font-data">{Math.round(operationStatus.cleanup.progress)}%</span>
                </div>
                <div className="w-full bg-background rounded-full h-2">
                  <div 
                    className="h-2 bg-warning rounded-full transition-all duration-300"
                    style={{ width: `${operationStatus.cleanup.progress}%` }}
                  />
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Import/Export */}
      <div className="bg-card rounded-lg border border-border p-6">
        <div className="flex items-center space-x-3 mb-6">
          <div className="w-10 h-10 bg-accent/10 rounded-lg flex items-center justify-center">
            <Icon name="ArrowUpDown" size={20} color="var(--color-accent)" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-foreground">Import/Export Data</h3>
            <p className="text-sm text-muted-foreground">Import and export trading data in various formats</p>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="space-y-4">
            <h4 className="text-sm font-medium text-foreground">Export Settings</h4>
            
            <Select
              label="Export Format"
              options={exportFormatOptions}
              value={importExportSettings.exportFormat}
              onChange={(value) => handleImportExportSettingChange('exportFormat', value)}
            />

            <Select
              label="Date Range"
              options={dateRangeOptions}
              value={importExportSettings.dateRange}
              onChange={(value) => handleImportExportSettingChange('dateRange', value)}
            />

            <Checkbox
              label="Include Metadata"
              description="Include system metadata in export"
              checked={importExportSettings.includeMetadata}
              onChange={(e) => handleImportExportSettingChange('includeMetadata', e.target.checked)}
            />
          </div>

          <div className="space-y-4">
            <h4 className="text-sm font-medium text-foreground">Import/Export Operations</h4>
            
            <div className="space-y-3">
              <Button
                variant="default"
                onClick={exportData}
                disabled={operationStatus.export.running}
                loading={operationStatus.export.running}
                iconName="Download"
                iconPosition="left"
                className="w-full"
              >
                Export Data
              </Button>

              <Button
                variant="outline"
                onClick={importData}
                disabled={operationStatus.import.running}
                loading={operationStatus.import.running}
                iconName="Upload"
                iconPosition="left"
                className="w-full"
              >
                Import Data
              </Button>

              {(operationStatus.export.running || operationStatus.import.running) && (
                <div className="bg-muted/50 rounded-lg p-3">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm text-muted-foreground">
                      {operationStatus.export.running ? 'Exporting data...' : 'Importing data...'}
                    </span>
                    <span className="text-sm font-data">
                      {Math.round(operationStatus.export.running ? operationStatus.export.progress : operationStatus.import.progress)}%
                    </span>
                  </div>
                  <div className="w-full bg-background rounded-full h-2">
                    <div 
                      className="h-2 bg-accent rounded-full transition-all duration-300"
                      style={{ 
                        width: `${operationStatus.export.running ? operationStatus.export.progress : operationStatus.import.progress}%` 
                      }}
                    />
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DatabaseManagement;