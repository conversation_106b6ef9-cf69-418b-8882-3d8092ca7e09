import React, { useState, useEffect } from 'react';
import Icon from '../../../components/AppIcon';
import Input from '../../../components/ui/Input';
import Button from '../../../components/ui/Button';

const CapitalInputForm = ({ 
  totalCapital, 
  onCapitalChange, 
  usedCapital, 
  availableCapital,
  onSaveCapital 
}) => {
  const [inputValue, setInputValue] = useState(totalCapital.toString());
  const [isEditing, setIsEditing] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    setInputValue(totalCapital.toString());
  }, [totalCapital]);

  const handleInputChange = (e) => {
    const value = e.target.value;
    setInputValue(value);
    
    // Validation
    const numValue = parseFloat(value);
    if (isNaN(numValue) || numValue <= 0) {
      setError('Please enter a valid amount greater than 0');
    } else if (numValue < usedCapital) {
      setError(`Capital cannot be less than used amount (₹${usedCapital.toLocaleString('en-IN')})`);
    } else {
      setError('');
    }
  };

  const handleSave = () => {
    const numValue = parseFloat(inputValue);
    if (!error && !isNaN(numValue) && numValue > 0) {
      onCapitalChange(numValue);
      onSaveCapital();
      setIsEditing(false);
    }
  };

  const handleCancel = () => {
    setInputValue(totalCapital.toString());
    setIsEditing(false);
    setError('');
  };

  const usagePercentage = totalCapital > 0 ? (usedCapital / totalCapital) * 100 : 0;

  return (
    <div className="bg-card rounded-lg border border-border p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
            <Icon name="Wallet" size={20} color="var(--color-primary)" />
          </div>
          <div>
            <h2 className="text-lg font-semibold text-foreground">Total Strategy Capital</h2>
            <p className="text-sm text-muted-foreground">Configure your total trading capital</p>
          </div>
        </div>
        {!isEditing && (
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsEditing(true)}
            iconName="Edit2"
            iconPosition="left"
          >
            Edit
          </Button>
        )}
      </div>

      {isEditing ? (
        <div className="space-y-4">
          <Input
            label="Total Capital Amount"
            type="number"
            placeholder="Enter total capital"
            value={inputValue}
            onChange={handleInputChange}
            error={error}
            required
            className="max-w-md"
          />
          <div className="flex space-x-3">
            <Button
              variant="default"
              size="sm"
              onClick={handleSave}
              disabled={!!error || !inputValue}
              iconName="Check"
              iconPosition="left"
            >
              Save
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleCancel}
              iconName="X"
              iconPosition="left"
            >
              Cancel
            </Button>
          </div>
        </div>
      ) : (
        <div className="space-y-4">
          <div className="flex items-baseline space-x-2">
            <span className="text-3xl font-bold text-foreground font-data">
              ₹{totalCapital.toLocaleString('en-IN')}
            </span>
            <span className="text-sm text-muted-foreground">Total Capital</span>
          </div>

          {/* Capital Usage Progress */}
          <div className="space-y-3">
            <div className="flex items-center justify-between text-sm">
              <span className="text-muted-foreground">Capital Usage</span>
              <span className="font-data font-medium">{usagePercentage.toFixed(1)}%</span>
            </div>
            
            <div className="w-full bg-muted rounded-full h-3">
              <div 
                className={`h-3 rounded-full transition-all duration-500 ${
                  usagePercentage > 90 ? 'bg-error' : 
                  usagePercentage > 75 ? 'bg-warning' : 
                  usagePercentage > 50 ? 'bg-accent' : 'bg-success'
                }`}
                style={{ width: `${Math.min(usagePercentage, 100)}%` }}
              />
            </div>

            <div className="grid grid-cols-2 gap-4 mt-4">
              <div className="bg-muted/50 rounded-lg p-3">
                <div className="flex items-center space-x-2 mb-1">
                  <Icon name="TrendingUp" size={16} color="var(--color-error)" />
                  <span className="text-sm text-muted-foreground">Used Capital</span>
                </div>
                <span className="text-lg font-semibold text-foreground font-data">
                  ₹{usedCapital.toLocaleString('en-IN')}
                </span>
              </div>
              
              <div className="bg-muted/50 rounded-lg p-3">
                <div className="flex items-center space-x-2 mb-1">
                  <Icon name="TrendingDown" size={16} color="var(--color-success)" />
                  <span className="text-sm text-muted-foreground">Available Capital</span>
                </div>
                <span className="text-lg font-semibold text-foreground font-data">
                  ₹{availableCapital.toLocaleString('en-IN')}
                </span>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CapitalInputForm;