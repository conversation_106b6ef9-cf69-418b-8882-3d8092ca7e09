import React, { useState } from 'react';

import Button from '../../../components/ui/Button';
import Select from '../../../components/ui/Select';
import Input from '../../../components/ui/Input';

const TradeFilters = ({ onFiltersChange, onExport, onReset }) => {
  const [filters, setFilters] = useState({
    dateRange: 'all',
    symbol: '',
    tradeType: 'all',
    profitLoss: 'all',
    startDate: '',
    endDate: ''
  });

  const dateRangeOptions = [
    { value: 'all', label: 'All Time' },
    { value: 'today', label: 'Today' },
    { value: 'week', label: 'This Week' },
    { value: 'month', label: 'This Month' },
    { value: 'quarter', label: 'This Quarter' },
    { value: 'year', label: 'This Year' },
    { value: 'custom', label: 'Custom Range' }
  ];

  const tradeTypeOptions = [
    { value: 'all', label: 'All Trades' },
    { value: 'buy', label: 'Buy Orders' },
    { value: 'sell', label: 'Sell Orders' }
  ];

  const profitLossOptions = [
    { value: 'all', label: 'All Trades' },
    { value: 'profit', label: 'Profitable' },
    { value: 'loss', label: 'Loss Making' },
    { value: 'breakeven', label: 'Break Even' }
  ];

  const handleFilterChange = (key, value) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);
    onFiltersChange(newFilters);
  };

  const handleReset = () => {
    const resetFilters = {
      dateRange: 'all',
      symbol: '',
      tradeType: 'all',
      profitLoss: 'all',
      startDate: '',
      endDate: ''
    };
    setFilters(resetFilters);
    onFiltersChange(resetFilters);
    onReset();
  };

  return (
    <div className="bg-card border border-border rounded-lg p-6 mb-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-foreground">Filter Trades</h3>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleReset}
            iconName="RotateCcw"
            iconPosition="left"
          >
            Reset
          </Button>
          <Button
            variant="default"
            size="sm"
            onClick={onExport}
            iconName="Download"
            iconPosition="left"
          >
            Export CSV
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-6 gap-4">
        {/* Date Range */}
        <div className="lg:col-span-2">
          <Select
            label="Date Range"
            options={dateRangeOptions}
            value={filters.dateRange}
            onChange={(value) => handleFilterChange('dateRange', value)}
            placeholder="Select date range"
          />
        </div>

        {/* Stock Symbol */}
        <div>
          <Input
            label="Stock Symbol"
            type="text"
            placeholder="e.g., RELIANCE"
            value={filters.symbol}
            onChange={(e) => handleFilterChange('symbol', e.target.value.toUpperCase())}
          />
        </div>

        {/* Trade Type */}
        <div>
          <Select
            label="Trade Type"
            options={tradeTypeOptions}
            value={filters.tradeType}
            onChange={(value) => handleFilterChange('tradeType', value)}
            placeholder="Select type"
          />
        </div>

        {/* Profit/Loss */}
        <div>
          <Select
            label="P&L Status"
            options={profitLossOptions}
            value={filters.profitLoss}
            onChange={(value) => handleFilterChange('profitLoss', value)}
            placeholder="Select status"
          />
        </div>

        {/* Search */}
        <div>
          <Input
            label="Quick Search"
            type="search"
            placeholder="Search trades..."
            value={filters.search || ''}
            onChange={(e) => handleFilterChange('search', e.target.value)}
          />
        </div>
      </div>

      {/* Custom Date Range */}
      {filters.dateRange === 'custom' && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4 pt-4 border-t border-border">
          <Input
            label="Start Date"
            type="date"
            value={filters.startDate}
            onChange={(e) => handleFilterChange('startDate', e.target.value)}
          />
          <Input
            label="End Date"
            type="date"
            value={filters.endDate}
            onChange={(e) => handleFilterChange('endDate', e.target.value)}
          />
        </div>
      )}
    </div>
  );
};

export default TradeFilters;