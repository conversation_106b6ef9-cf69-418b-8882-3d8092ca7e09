import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';
import Input from '../../../components/ui/Input';
import Select from '../../../components/ui/Select';

const ManualOrderForm = ({ isOpen, onClose, onCreateOrder }) => {
  const [formData, setFormData] = useState({
    symbol: '',
    orderType: 'buy',
    triggerPrice: '',
    quantity: '',
    orderSubType: 'target',
    validityDays: '30'
  });
  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const orderTypeOptions = [
    { value: 'buy', label: 'Buy Order' },
    { value: 'sell', label: 'Sell Order' }
  ];

  const orderSubTypeOptions = [
    { value: 'target', label: 'Target Order' },
    { value: 'stop-loss', label: 'Stop Loss' },
    { value: 'breakout', label: 'Breakout' }
  ];

  const validityOptions = [
    { value: '1', label: '1 Day' },
    { value: '7', label: '1 Week' },
    { value: '30', label: '1 Month' },
    { value: '90', label: '3 Months' }
  ];

  const stockSymbols = [
    { value: 'RELIANCE', label: 'RELIANCE - Reliance Industries' },
    { value: 'TCS', label: 'TCS - Tata Consultancy Services' },
    { value: 'INFY', label: 'INFY - Infosys Limited' },
    { value: 'HDFCBANK', label: 'HDFCBANK - HDFC Bank' },
    { value: 'ICICIBANK', label: 'ICICIBANK - ICICI Bank' },
    { value: 'HINDUNILVR', label: 'HINDUNILVR - Hindustan Unilever' },
    { value: 'ITC', label: 'ITC - ITC Limited' },
    { value: 'SBIN', label: 'SBIN - State Bank of India' },
    { value: 'BHARTIARTL', label: 'BHARTIARTL - Bharti Airtel' },
    { value: 'KOTAKBANK', label: 'KOTAKBANK - Kotak Mahindra Bank' }
  ];

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.symbol) {
      newErrors.symbol = 'Please select a stock symbol';
    }

    if (!formData.triggerPrice) {
      newErrors.triggerPrice = 'Trigger price is required';
    } else if (parseFloat(formData.triggerPrice) <= 0) {
      newErrors.triggerPrice = 'Trigger price must be greater than 0';
    }

    if (!formData.quantity) {
      newErrors.quantity = 'Quantity is required';
    } else if (parseInt(formData.quantity) <= 0) {
      newErrors.quantity = 'Quantity must be greater than 0';
    }

    // Business rule validations
    const investment = parseFloat(formData.triggerPrice) * parseInt(formData.quantity);
    if (investment > 2000) {
      newErrors.quantity = 'Investment cannot exceed ₹2,000 per entry';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    
    try {
      const orderData = {
        ...formData,
        triggerPrice: parseFloat(formData.triggerPrice),
        quantity: parseInt(formData.quantity),
        investment: parseFloat(formData.triggerPrice) * parseInt(formData.quantity),
        createdDate: new Date().toLocaleDateString('en-IN'),
        createdTime: new Date().toLocaleTimeString('en-IN', { hour12: false }),
        status: 'Pending',
        id: Date.now().toString()
      };

      await onCreateOrder(orderData);
      
      // Reset form
      setFormData({
        symbol: '',
        orderType: 'buy',
        triggerPrice: '',
        quantity: '',
        orderSubType: 'target',
        validityDays: '30'
      });
      
      onClose();
    } catch (error) {
      console.error('Error creating order:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const calculateInvestment = () => {
    const price = parseFloat(formData.triggerPrice) || 0;
    const qty = parseInt(formData.quantity) || 0;
    return price * qty;
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 2
    }).format(amount);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-[1000] bg-black/50 flex items-center justify-center p-4">
      <div className="bg-card rounded-lg shadow-trading-lg max-w-md w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6 border-b border-border">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                <Icon name="Plus" size={20} color="var(--color-primary)" />
              </div>
              <div>
                <h2 className="text-lg font-semibold text-foreground">Create Manual GTT Order</h2>
                <p className="text-sm text-muted-foreground">For testing and custom scenarios</p>
              </div>
            </div>
            <Button
              variant="ghost"
              size="icon"
              onClick={onClose}
            >
              <Icon name="X" size={20} />
            </Button>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Stock Symbol */}
          <Select
            label="Stock Symbol"
            description="Select the stock for GTT order"
            options={stockSymbols}
            value={formData.symbol}
            onChange={(value) => handleInputChange('symbol', value)}
            error={errors.symbol}
            required
            searchable
            placeholder="Search and select stock..."
          />

          {/* Order Type */}
          <Select
            label="Order Type"
            description="Choose buy or sell order"
            options={orderTypeOptions}
            value={formData.orderType}
            onChange={(value) => handleInputChange('orderType', value)}
            required
          />

          {/* Order Sub Type */}
          <Select
            label="Order Sub Type"
            description="Specify the order strategy"
            options={orderSubTypeOptions}
            value={formData.orderSubType}
            onChange={(value) => handleInputChange('orderSubType', value)}
            required
          />

          <div className="grid grid-cols-2 gap-4">
            {/* Trigger Price */}
            <Input
              label="Trigger Price"
              type="number"
              step="0.01"
              min="0"
              placeholder="0.00"
              value={formData.triggerPrice}
              onChange={(e) => handleInputChange('triggerPrice', e.target.value)}
              error={errors.triggerPrice}
              required
            />

            {/* Quantity */}
            <Input
              label="Quantity"
              type="number"
              min="1"
              placeholder="0"
              value={formData.quantity}
              onChange={(e) => handleInputChange('quantity', e.target.value)}
              error={errors.quantity}
              required
            />
          </div>

          {/* Investment Calculation */}
          {formData.triggerPrice && formData.quantity && (
            <div className="bg-muted/50 rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm text-muted-foreground">Total Investment</span>
                <span className="text-sm font-data font-medium text-foreground">
                  {formatCurrency(calculateInvestment())}
                </span>
              </div>
              {calculateInvestment() > 2000 && (
                <div className="flex items-center space-x-2 text-error">
                  <Icon name="AlertTriangle" size={14} />
                  <span className="text-xs">Exceeds ₹2,000 per entry limit</span>
                </div>
              )}
            </div>
          )}

          {/* Validity */}
          <Select
            label="Validity Period"
            description="How long should the order remain active"
            options={validityOptions}
            value={formData.validityDays}
            onChange={(value) => handleInputChange('validityDays', value)}
            required
          />

          {/* Business Rules Info */}
          <div className="bg-accent/5 border border-accent/20 rounded-lg p-4">
            <div className="flex items-start space-x-3">
              <Icon name="Info" size={16} color="var(--color-accent)" className="mt-0.5" />
              <div className="text-sm">
                <p className="text-accent font-medium mb-1">Trading Limits</p>
                <div className="text-muted-foreground space-y-1">
                  <p>• Maximum ₹2,000 per entry</p>
                  <p>• Maximum 5 entries per stock</p>
                  <p>• Maximum ₹10,000 total per stock</p>
                </div>
              </div>
            </div>
          </div>

          {/* Form Actions */}
          <div className="flex space-x-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              className="flex-1"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              loading={isSubmitting}
              iconName="Plus"
              iconPosition="left"
              className="flex-1"
            >
              Create Order
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ManualOrderForm;