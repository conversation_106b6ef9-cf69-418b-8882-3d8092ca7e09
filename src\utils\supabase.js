import { createClient } from '@supabase/supabase-js';

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables. Please check your .env file.');
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Auth helpers
export const auth = {
  signUp: (email, password, options = {}) => 
    supabase.auth.signUp({ email, password, options }),
    
  signIn: (email, password) => 
    supabase.auth.signInWithPassword({ email, password }),
    
  signOut: () => 
    supabase.auth.signOut(),
    
  getSession: () => 
    supabase.auth.getSession(),
    
  getUser: () => 
    supabase.auth.getUser(),
    
  onAuthStateChange: (callback) => 
    supabase.auth.onAuthStateChange(callback)
};

export default supabase;