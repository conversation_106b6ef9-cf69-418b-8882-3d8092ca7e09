import React, { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import Icon from '../AppIcon';
import Button from './Button';

const Sidebar = () => {
  const location = useLocation();
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [capitalUsage, setCapitalUsage] = useState(68);
  const [activeStrategies, setActiveStrategies] = useState(3);
  const [todayPnL, setTodayPnL] = useState(2450);
  const [recentTrades, setRecentTrades] = useState([
    { symbol: 'RELIANCE', type: 'BUY', price: 2456.75, time: '14:32' },
    { symbol: 'TCS', type: 'SELL', price: 3234.50, time: '14:28' },
    { symbol: 'INFY', type: 'BUY', price: 1567.25, time: '14:15' }
  ]);

  useEffect(() => {
    const savedState = localStorage.getItem('sidebar-collapsed');
    if (savedState) {
      setIsCollapsed(JSON.parse(savedState));
    }
  }, []);

  const toggleSidebar = () => {
    const newState = !isCollapsed;
    setIsCollapsed(newState);
    localStorage.setItem('sidebar-collapsed', JSON.stringify(newState));
  };

  const quickActions = [
    { icon: 'Plus', label: 'New GTT', action: () => console.log('New GTT') },
    { icon: 'Pause', label: 'Pause All', action: () => console.log('Pause All') },
    { icon: 'RefreshCw', label: 'Refresh', action: () => console.log('Refresh') }
  ];

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const formatPrice = (price) => {
    return new Intl.NumberFormat('en-IN', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(price);
  };

  return (
    <>
      {/* Sidebar */}
      <aside className={`fixed left-0 top-16 bottom-0 z-40 bg-card border-r border-border transition-all duration-300 ease-in-out ${
        isCollapsed ? 'w-16' : 'w-80'
      } lg:translate-x-0 ${isCollapsed ? 'translate-x-0' : 'translate-x-0'}`}>
        
        {/* Toggle Button */}
        <div className="absolute -right-3 top-6 z-50">
          <Button
            variant="outline"
            size="icon"
            onClick={toggleSidebar}
            className="w-6 h-6 rounded-full bg-card border shadow-trading"
          >
            <Icon 
              name={isCollapsed ? "ChevronRight" : "ChevronLeft"} 
              size={12} 
            />
          </Button>
        </div>

        <div className="p-4 h-full overflow-y-auto">
          {!isCollapsed ? (
            <div className="space-y-6">
              {/* Quick Stats */}
              <div className="space-y-4">
                <h3 className="text-sm font-medium text-muted-foreground uppercase tracking-wide">
                  Quick Overview
                </h3>
                
                {/* Capital Usage */}
                <div className="bg-muted/50 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm text-muted-foreground">Capital Usage</span>
                    <span className="text-sm font-data font-medium">{capitalUsage}%</span>
                  </div>
                  <div className="w-full bg-background rounded-full h-2">
                    <div 
                      className={`h-2 rounded-full transition-all duration-300 ${
                        capitalUsage > 80 ? 'bg-warning' : capitalUsage > 60 ? 'bg-accent' : 'bg-success'
                      }`}
                      style={{ width: `${capitalUsage}%` }}
                    />
                  </div>
                </div>

                {/* Today's P&L */}
                <div className="bg-muted/50 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">Today's P&L</span>
                    <div className="flex items-center space-x-1">
                      <Icon 
                        name={todayPnL >= 0 ? "TrendingUp" : "TrendingDown"} 
                        size={14} 
                        color={todayPnL >= 0 ? "var(--color-success)" : "var(--color-error)"}
                      />
                      <span className={`text-sm font-data font-medium ${
                        todayPnL >= 0 ? 'text-success' : 'text-error'
                      }`}>
                        {formatCurrency(Math.abs(todayPnL))}
                      </span>
                    </div>
                  </div>
                </div>

                {/* Active Strategies */}
                <div className="bg-muted/50 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">Active Strategies</span>
                    <div className="flex items-center space-x-2">
                      <div className="w-2 h-2 bg-success rounded-full animate-pulse-trading" />
                      <span className="text-sm font-data font-medium">{activeStrategies}</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Quick Actions */}
              <div className="space-y-3">
                <h3 className="text-sm font-medium text-muted-foreground uppercase tracking-wide">
                  Quick Actions
                </h3>
                <div className="grid grid-cols-1 gap-2">
                  {quickActions.map((action, index) => (
                    <Button
                      key={index}
                      variant="outline"
                      size="sm"
                      onClick={action.action}
                      className="justify-start"
                    >
                      <Icon name={action.icon} size={16} />
                      {action.label}
                    </Button>
                  ))}
                </div>
              </div>

              {/* Recent Trades */}
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <h3 className="text-sm font-medium text-muted-foreground uppercase tracking-wide">
                    Recent Trades
                  </h3>
                  <Link 
                    to="/trade-log" 
                    className="text-xs text-accent hover:text-accent/80 transition-colors"
                  >
                    View All
                  </Link>
                </div>
                <div className="space-y-2">
                  {recentTrades.map((trade, index) => (
                    <div key={index} className="bg-muted/50 rounded-lg p-3">
                      <div className="flex items-center justify-between mb-1">
                        <span className="text-sm font-medium">{trade.symbol}</span>
                        <span className="text-xs text-muted-foreground font-data">
                          {trade.time}
                        </span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className={`text-xs px-2 py-1 rounded-full font-medium ${
                          trade.type === 'BUY' ?'bg-success/10 text-success' :'bg-error/10 text-error'
                        }`}>
                          {trade.type}
                        </span>
                        <span className="text-sm font-data">
                          ₹{formatPrice(trade.price)}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Market Status */}
              <div className="space-y-3">
                <h3 className="text-sm font-medium text-muted-foreground uppercase tracking-wide">
                  Market Status
                </h3>
                <div className="bg-muted/50 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">NSE</span>
                    <div className="flex items-center space-x-2">
                      <div className="w-2 h-2 bg-success rounded-full animate-pulse-trading" />
                      <span className="text-sm font-medium text-success">Open</span>
                    </div>
                  </div>
                  <div className="mt-2 text-xs text-muted-foreground">
                    Closes at 15:30
                  </div>
                </div>
              </div>
            </div>
          ) : (
            /* Collapsed State */
            <div className="space-y-4 pt-4">
              {/* Collapsed Quick Actions */}
              <div className="space-y-2">
                {quickActions.map((action, index) => (
                  <Button
                    key={index}
                    variant="ghost"
                    size="icon"
                    onClick={action.action}
                    className="w-8 h-8"
                    title={action.label}
                  >
                    <Icon name={action.icon} size={16} />
                  </Button>
                ))}
              </div>

              {/* Collapsed Stats Indicators */}
              <div className="space-y-3 pt-4 border-t border-border">
                <div className="flex justify-center" title={`Capital Usage: ${capitalUsage}%`}>
                  <div className="w-8 h-1 bg-background rounded-full overflow-hidden">
                    <div 
                      className={`h-full transition-all duration-300 ${
                        capitalUsage > 80 ? 'bg-warning' : capitalUsage > 60 ? 'bg-accent' : 'bg-success'
                      }`}
                      style={{ width: `${capitalUsage}%` }}
                    />
                  </div>
                </div>
                
                <div className="flex justify-center" title={`Today's P&L: ${formatCurrency(todayPnL)}`}>
                  <Icon 
                    name={todayPnL >= 0 ? "TrendingUp" : "TrendingDown"} 
                    size={16} 
                    color={todayPnL >= 0 ? "var(--color-success)" : "var(--color-error)"}
                  />
                </div>
                
                <div className="flex justify-center" title={`Active Strategies: ${activeStrategies}`}>
                  <div className="relative">
                    <Icon name="Activity" size={16} color="var(--color-success)" />
                    <div className="absolute -top-1 -right-1 w-3 h-3 bg-success rounded-full flex items-center justify-center">
                      <span className="text-[8px] text-white font-bold">{activeStrategies}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </aside>

      {/* Main Content Offset */}
      <div className={`transition-all duration-300 ease-in-out ${
        isCollapsed ? 'lg:ml-16' : 'lg:ml-80'
      }`} />
    </>
  );
};

export default Sidebar;