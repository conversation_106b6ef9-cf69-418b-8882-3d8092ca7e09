import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const QuickActions = ({ onManualSignal, onEmergencyPause, onRefreshData }) => {
  const [isGeneratingSignal, setIsGeneratingSignal] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);

  const handleManualSignal = async () => {
    setIsGeneratingSignal(true);
    try {
      await onManualSignal();
    } finally {
      setIsGeneratingSignal(false);
    }
  };

  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      await onRefreshData();
    } finally {
      setIsRefreshing(false);
    }
  };

  const quickActionItems = [
    {
      title: 'Generate Signal',
      description: 'Run manual scan for breakout stocks',
      icon: 'Zap',
      action: handleManualSignal,
      loading: isGeneratingSignal,
      variant: 'default',
      color: 'primary'
    },
    {
      title: 'Emergency Pause',
      description: 'Stop all trading activities immediately',
      icon: 'Square',
      action: onEmergencyPause,
      loading: false,
      variant: 'destructive',
      color: 'error'
    },
    {
      title: 'Refresh Data',
      description: 'Update all prices and portfolio values',
      icon: 'RefreshCw',
      action: handleRefresh,
      loading: isRefreshing,
      variant: 'outline',
      color: 'accent'
    },
    {
      title: 'View Reports',
      description: 'Access detailed performance analytics',
      icon: 'BarChart3',
      action: () => console.log('View Reports'),
      loading: false,
      variant: 'outline',
      color: 'secondary'
    }
  ];

  return (
    <div className="bg-card border border-border rounded-lg shadow-trading">
      <div className="p-6 border-b border-border">
        <h3 className="text-lg font-semibold text-foreground">Quick Actions</h3>
        <p className="text-sm text-muted-foreground">
          Frequently used trading controls
        </p>
      </div>

      <div className="p-6">
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          {quickActionItems.map((item, index) => (
            <div key={index} className="group">
              <Button
                variant={item.variant}
                onClick={item.action}
                loading={item.loading}
                fullWidth
                className="h-auto p-4 flex-col items-start space-y-2"
              >
                <div className="flex items-center space-x-3 w-full">
                  <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${
                    item.variant === 'destructive' ? 'bg-error/10' :
                    item.variant === 'outline' ? 'bg-muted' : 'bg-primary/10'
                  }`}>
                    <Icon 
                      name={item.icon} 
                      size={20} 
                      color={
                        item.variant === 'destructive' ? 'var(--color-error)' :
                        item.variant === 'outline' ? 'var(--color-muted-foreground)' : 'var(--color-primary)'
                      }
                    />
                  </div>
                  <div className="flex-1 text-left">
                    <div className="font-medium">{item.title}</div>
                    <div className="text-xs opacity-80 mt-1">
                      {item.description}
                    </div>
                  </div>
                </div>
              </Button>
            </div>
          ))}
        </div>

        <div className="mt-6 p-4 bg-accent/5 border border-accent/20 rounded-lg">
          <div className="flex items-start space-x-3">
            <Icon name="Lightbulb" size={16} color="var(--color-accent)" className="mt-0.5" />
            <div className="text-sm">
              <p className="font-medium text-foreground mb-1">Pro Tip:</p>
              <p className="text-muted-foreground">
                Use manual signal generation during market hours to test the strategy with current market conditions. 
                Emergency pause will cancel all pending GTT orders.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default QuickActions;