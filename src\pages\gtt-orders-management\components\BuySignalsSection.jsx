import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const BuySignalsSection = ({ buySignals, onCreateOrder, onDeleteSignal }) => {
  const [sortBy, setSortBy] = useState('symbol');
  const [sortOrder, setSortOrder] = useState('asc');
  const [selectedSignals, setSelectedSignals] = useState([]);

  const sortedSignals = [...buySignals].sort((a, b) => {
    const aValue = a[sortBy];
    const bValue = b[sortBy];
    
    if (sortBy === 'triggerPrice' || sortBy === 'suggestedQuantity') {
      return sortOrder === 'asc' ? aValue - bValue : bValue - aValue;
    }
    
    return sortOrder === 'asc' 
      ? aValue.localeCompare(bValue)
      : bValue.localeCompare(aValue);
  });

  const handleSort = (field) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortOrder('asc');
    }
  };

  const handleSelectAll = (checked) => {
    setSelectedSignals(checked ? buySignals.map(signal => signal.id) : []);
  };

  const handleSelectSignal = (signalId, checked) => {
    if (checked) {
      setSelectedSignals([...selectedSignals, signalId]);
    } else {
      setSelectedSignals(selectedSignals.filter(id => id !== signalId));
    }
  };

  const handleBulkDelete = () => {
    selectedSignals.forEach(signalId => {
      onDeleteSignal(signalId);
    });
    setSelectedSignals([]);
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 2
    }).format(amount);
  };

  const getSortIcon = (field) => {
    if (sortBy !== field) return 'ArrowUpDown';
    return sortOrder === 'asc' ? 'ArrowUp' : 'ArrowDown';
  };

  return (
    <div className="bg-card rounded-lg border border-border">
      <div className="p-6 border-b border-border">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-success/10 rounded-lg flex items-center justify-center">
              <Icon name="TrendingUp" size={20} color="var(--color-success)" />
            </div>
            <div>
              <h2 className="text-lg font-semibold text-foreground">Active Buy Signals</h2>
              <p className="text-sm text-muted-foreground">
                {buySignals.length} signals ready for GTT order creation
              </p>
            </div>
          </div>
          
          {selectedSignals.length > 0 && (
            <div className="flex items-center space-x-2">
              <span className="text-sm text-muted-foreground">
                {selectedSignals.length} selected
              </span>
              <Button
                variant="destructive"
                size="sm"
                onClick={handleBulkDelete}
                iconName="Trash2"
                iconPosition="left"
              >
                Delete Selected
              </Button>
            </div>
          )}
        </div>

        {/* Business Rules Banner */}
        <div className="bg-accent/5 border border-accent/20 rounded-lg p-4 mb-4">
          <div className="flex items-start space-x-3">
            <Icon name="Info" size={16} color="var(--color-accent)" className="mt-0.5" />
            <div className="text-sm">
              <p className="text-accent font-medium mb-1">Trading Limits</p>
              <div className="text-muted-foreground space-y-1">
                <p>• Maximum ₹2,000 per entry • Maximum 5 entries per stock • Maximum ₹10,000 total per stock</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="p-6">
        {buySignals.length === 0 ? (
          <div className="text-center py-12">
            <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center mx-auto mb-4">
              <Icon name="TrendingUp" size={24} color="var(--color-muted-foreground)" />
            </div>
            <h3 className="text-lg font-medium text-foreground mb-2">No Active Buy Signals</h3>
            <p className="text-muted-foreground mb-4">
              Buy signals will appear here when stocks break their 52-week highs
            </p>
            <Button variant="outline" iconName="RefreshCw" iconPosition="left">
              Refresh Signals
            </Button>
          </div>
        ) : (
          <>
            {/* Desktop Table */}
            <div className="hidden md:block overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-border">
                    <th className="text-left py-3 px-2">
                      <input
                        type="checkbox"
                        checked={selectedSignals.length === buySignals.length}
                        onChange={(e) => handleSelectAll(e.target.checked)}
                        className="rounded border-border"
                      />
                    </th>
                    <th className="text-left py-3 px-4">
                      <button
                        onClick={() => handleSort('symbol')}
                        className="flex items-center space-x-1 text-sm font-medium text-muted-foreground hover:text-foreground"
                      >
                        <span>Symbol</span>
                        <Icon name={getSortIcon('symbol')} size={14} />
                      </button>
                    </th>
                    <th className="text-left py-3 px-4">
                      <button
                        onClick={() => handleSort('triggerPrice')}
                        className="flex items-center space-x-1 text-sm font-medium text-muted-foreground hover:text-foreground"
                      >
                        <span>Trigger Price</span>
                        <Icon name={getSortIcon('triggerPrice')} size={14} />
                      </button>
                    </th>
                    <th className="text-left py-3 px-4">
                      <button
                        onClick={() => handleSort('suggestedQuantity')}
                        className="flex items-center space-x-1 text-sm font-medium text-muted-foreground hover:text-foreground"
                      >
                        <span>Suggested Qty</span>
                        <Icon name={getSortIcon('suggestedQuantity')} size={14} />
                      </button>
                    </th>
                    <th className="text-left py-3 px-4">
                      <span className="text-sm font-medium text-muted-foreground">Investment</span>
                    </th>
                    <th className="text-left py-3 px-4">
                      <span className="text-sm font-medium text-muted-foreground">Signal Date</span>
                    </th>
                    <th className="text-left py-3 px-4">
                      <span className="text-sm font-medium text-muted-foreground">Status</span>
                    </th>
                    <th className="text-right py-3 px-4">
                      <span className="text-sm font-medium text-muted-foreground">Actions</span>
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {sortedSignals.map((signal) => (
                    <tr key={signal.id} className="border-b border-border hover:bg-muted/50">
                      <td className="py-4 px-2">
                        <input
                          type="checkbox"
                          checked={selectedSignals.includes(signal.id)}
                          onChange={(e) => handleSelectSignal(signal.id, e.target.checked)}
                          className="rounded border-border"
                        />
                      </td>
                      <td className="py-4 px-4">
                        <div className="flex items-center space-x-3">
                          <div className="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center">
                            <span className="text-xs font-medium text-primary">
                              {signal.symbol.charAt(0)}
                            </span>
                          </div>
                          <div>
                            <p className="font-medium text-foreground">{signal.symbol}</p>
                            <p className="text-xs text-muted-foreground">{signal.exchange}</p>
                          </div>
                        </div>
                      </td>
                      <td className="py-4 px-4">
                        <div className="text-sm">
                          <p className="font-data font-medium text-foreground">
                            {formatCurrency(signal.triggerPrice)}
                          </p>
                          <p className="text-xs text-success">52W High</p>
                        </div>
                      </td>
                      <td className="py-4 px-4">
                        <div className="text-sm">
                          <p className="font-data font-medium text-foreground">
                            {signal.suggestedQuantity}
                          </p>
                          <p className="text-xs text-muted-foreground">shares</p>
                        </div>
                      </td>
                      <td className="py-4 px-4">
                        <div className="text-sm">
                          <p className="font-data font-medium text-foreground">
                            {formatCurrency(signal.investment)}
                          </p>
                          <p className="text-xs text-muted-foreground">
                            {signal.entriesUsed}/5 entries
                          </p>
                        </div>
                      </td>
                      <td className="py-4 px-4">
                        <div className="text-sm">
                          <p className="text-foreground">{signal.signalDate}</p>
                          <p className="text-xs text-muted-foreground">{signal.signalTime}</p>
                        </div>
                      </td>
                      <td className="py-4 px-4">
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                          signal.status === 'Active' ?'bg-success/10 text-success' :'bg-warning/10 text-warning'
                        }`}>
                          {signal.status}
                        </span>
                      </td>
                      <td className="py-4 px-4 text-right">
                        <div className="flex items-center justify-end space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => onCreateOrder(signal)}
                            iconName="Plus"
                            iconPosition="left"
                          >
                            Create GTT
                          </Button>
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => onDeleteSignal(signal.id)}
                          >
                            <Icon name="Trash2" size={16} />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Mobile Cards */}
            <div className="md:hidden space-y-4">
              {sortedSignals.map((signal) => (
                <div key={signal.id} className="bg-muted/50 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-3">
                      <input
                        type="checkbox"
                        checked={selectedSignals.includes(signal.id)}
                        onChange={(e) => handleSelectSignal(signal.id, e.target.checked)}
                        className="rounded border-border"
                      />
                      <div className="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center">
                        <span className="text-xs font-medium text-primary">
                          {signal.symbol.charAt(0)}
                        </span>
                      </div>
                      <div>
                        <p className="font-medium text-foreground">{signal.symbol}</p>
                        <p className="text-xs text-muted-foreground">{signal.exchange}</p>
                      </div>
                    </div>
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                      signal.status === 'Active' ?'bg-success/10 text-success' :'bg-warning/10 text-warning'
                    }`}>
                      {signal.status}
                    </span>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4 mb-4">
                    <div>
                      <p className="text-xs text-muted-foreground mb-1">Trigger Price</p>
                      <p className="font-data font-medium text-foreground">
                        {formatCurrency(signal.triggerPrice)}
                      </p>
                    </div>
                    <div>
                      <p className="text-xs text-muted-foreground mb-1">Quantity</p>
                      <p className="font-data font-medium text-foreground">
                        {signal.suggestedQuantity}
                      </p>
                    </div>
                    <div>
                      <p className="text-xs text-muted-foreground mb-1">Investment</p>
                      <p className="font-data font-medium text-foreground">
                        {formatCurrency(signal.investment)}
                      </p>
                    </div>
                    <div>
                      <p className="text-xs text-muted-foreground mb-1">Entries</p>
                      <p className="font-data font-medium text-foreground">
                        {signal.entriesUsed}/5
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div className="text-xs text-muted-foreground">
                      {signal.signalDate} • {signal.signalTime}
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => onCreateOrder(signal)}
                        iconName="Plus"
                        iconPosition="left"
                      >
                        Create GTT
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => onDeleteSignal(signal.id)}
                      >
                        <Icon name="Trash2" size={16} />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default BuySignalsSection;