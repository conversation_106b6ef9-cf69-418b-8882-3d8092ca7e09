import React, { useState, useEffect } from 'react';
import Header from '../../components/ui/Header';
import Sidebar from '../../components/ui/Sidebar';
import CapitalInputForm from './components/CapitalInputForm';
import LimitControls from './components/LimitControls';
import CapitalUsageTable from './components/CapitalUsageTable';
import EmergencyControls from './components/EmergencyControls';
import HistoricalChart from './components/HistoricalChart';

const CapitalManagement = () => {
  const [totalCapital, setTotalCapital] = useState(150000);
  const [usedCapital, setUsedCapital] = useState(102000);
  const [perStockLimit, setPerStockLimit] = useState(10000);
  const [perTradeLimit, setPerTradeLimit] = useState(2000);
  const [maxEntriesPerStock, setMaxEntriesPerStock] = useState(5);
  const [isStrategyActive, setIsStrategyActive] = useState(true);
  const [lastSaved, setLastSaved] = useState(new Date());

  // Mock capital usage data
  const [capitalUsage] = useState([
    {
      symbol: 'RELIANCE',
      companyName: 'Reliance Industries Ltd',
      allocatedAmount: 8500,
      entries: 3,
      remainingCapacity: 1500,
      utilizationPercentage: 85,
      averagePrice: 2456.75,
      totalQuantity: 12,
      currentPnL: 1250,
      nextEntryPrice: 2380.50
    },
    {
      symbol: 'TCS',
      companyName: 'Tata Consultancy Services',
      allocatedAmount: 9200,
      entries: 4,
      remainingCapacity: 800,
      utilizationPercentage: 92,
      averagePrice: 3234.50,
      totalQuantity: 8,
      currentPnL: -450,
      nextEntryPrice: 3150.25
    },
    {
      symbol: 'INFY',
      companyName: 'Infosys Limited',
      allocatedAmount: 7800,
      entries: 3,
      remainingCapacity: 2200,
      utilizationPercentage: 78,
      averagePrice: 1567.25,
      totalQuantity: 15,
      currentPnL: 890,
      nextEntryPrice: 1520.80
    },
    {
      symbol: 'HDFC',
      companyName: 'HDFC Bank Limited',
      allocatedAmount: 6500,
      entries: 2,
      remainingCapacity: 3500,
      utilizationPercentage: 65,
      averagePrice: 1678.90,
      totalQuantity: 10,
      currentPnL: 320,
      nextEntryPrice: 1635.45
    },
    {
      symbol: 'ICICIBANK',
      companyName: 'ICICI Bank Limited',
      allocatedAmount: 5200,
      entries: 2,
      remainingCapacity: 4800,
      utilizationPercentage: 52,
      averagePrice: 1234.60,
      totalQuantity: 12,
      currentPnL: 180,
      nextEntryPrice: 1198.75
    },
    {
      symbol: 'WIPRO',
      companyName: 'Wipro Limited',
      allocatedAmount: 4800,
      entries: 2,
      remainingCapacity: 5200,
      utilizationPercentage: 48,
      averagePrice: 567.80,
      totalQuantity: 20,
      currentPnL: -120,
      nextEntryPrice: 552.35
    },
    {
      symbol: 'MARUTI',
      companyName: 'Maruti Suzuki India Ltd',
      allocatedAmount: 9800,
      entries: 4,
      remainingCapacity: 200,
      utilizationPercentage: 98,
      averagePrice: 12456.75,
      totalQuantity: 2,
      currentPnL: 2100,
      nextEntryPrice: 12100.50
    },
    {
      symbol: 'BHARTIARTL',
      companyName: 'Bharti Airtel Limited',
      allocatedAmount: 6200,
      entries: 3,
      remainingCapacity: 3800,
      utilizationPercentage: 62,
      averagePrice: 1089.45,
      totalQuantity: 16,
      currentPnL: 450,
      nextEntryPrice: 1060.20
    },
    {
      symbol: 'HCLTECH',
      companyName: 'HCL Technologies Limited',
      allocatedAmount: 7500,
      entries: 3,
      remainingCapacity: 2500,
      utilizationPercentage: 75,
      averagePrice: 1789.30,
      totalQuantity: 11,
      currentPnL: 680,
      nextEntryPrice: 1740.85
    },
    {
      symbol: 'ASIANPAINT',
      companyName: 'Asian Paints Limited',
      allocatedAmount: 8900,
      entries: 4,
      remainingCapacity: 1100,
      utilizationPercentage: 89,
      averagePrice: 3456.80,
      totalQuantity: 7,
      currentPnL: 1150,
      nextEntryPrice: 3365.25
    }
  ]);

  const availableCapital = totalCapital - usedCapital;

  useEffect(() => {
    // Load saved settings from localStorage
    const savedSettings = localStorage.getItem('capital-management-settings');
    if (savedSettings) {
      const settings = JSON.parse(savedSettings);
      setTotalCapital(settings.totalCapital || 150000);
      setPerStockLimit(settings.perStockLimit || 10000);
      setPerTradeLimit(settings.perTradeLimit || 2000);
      setMaxEntriesPerStock(settings.maxEntriesPerStock || 5);
      setIsStrategyActive(settings.isStrategyActive !== undefined ? settings.isStrategyActive : true);
    }
  }, []);

  const saveSettings = () => {
    const settings = {
      totalCapital,
      perStockLimit,
      perTradeLimit,
      maxEntriesPerStock,
      isStrategyActive,
      lastSaved: new Date().toISOString()
    };
    localStorage.setItem('capital-management-settings', JSON.stringify(settings));
    setLastSaved(new Date());
  };

  const handleCapitalChange = (newCapital) => {
    setTotalCapital(newCapital);
  };

  const handleLimitsChange = (newLimits) => {
    setPerStockLimit(newLimits.perStockLimit);
    setPerTradeLimit(newLimits.perTradeLimit);
    setMaxEntriesPerStock(newLimits.maxEntriesPerStock);
  };

  const handleToggleStrategy = () => {
    setIsStrategyActive(!isStrategyActive);
  };

  const handleWithdrawCapital = (amount) => {
    const newTotal = totalCapital - amount;
    setTotalCapital(newTotal);
    console.log(`Withdrew ₹${amount.toLocaleString('en-IN')} from capital`);
  };

  // Auto-save settings when they change
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      saveSettings();
    }, 1000);

    return () => clearTimeout(timeoutId);
  }, [totalCapital, perStockLimit, perTradeLimit, maxEntriesPerStock, isStrategyActive]);

  return (
    <div className="min-h-screen bg-background">
      <Header />
      <Sidebar />
      
      <main className="pt-16 lg:ml-80">
        <div className="p-6 space-y-6">
          {/* Page Header */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-foreground">Capital Management</h1>
              <p className="text-muted-foreground">
                Configure and monitor your trading capital allocation
              </p>
            </div>
            <div className="text-right">
              <div className="text-sm text-muted-foreground">Last saved</div>
              <div className="text-sm font-medium text-foreground">
                {lastSaved.toLocaleTimeString('en-IN', { 
                  hour12: false, 
                  hour: '2-digit', 
                  minute: '2-digit' 
                })}
              </div>
            </div>
          </div>

          {/* Capital Input Form */}
          <CapitalInputForm
            totalCapital={totalCapital}
            onCapitalChange={handleCapitalChange}
            usedCapital={usedCapital}
            availableCapital={availableCapital}
            onSaveCapital={saveSettings}
          />

          {/* Limit Controls */}
          <LimitControls
            perStockLimit={perStockLimit}
            perTradeLimit={perTradeLimit}
            maxEntriesPerStock={maxEntriesPerStock}
            onLimitsChange={handleLimitsChange}
          />

          {/* Emergency Controls */}
          <EmergencyControls
            isStrategyActive={isStrategyActive}
            onToggleStrategy={handleToggleStrategy}
            onWithdrawCapital={handleWithdrawCapital}
            totalCapital={totalCapital}
            usedCapital={usedCapital}
          />

          {/* Capital Usage Table */}
          <CapitalUsageTable
            capitalUsage={capitalUsage}
            perStockLimit={perStockLimit}
          />

          {/* Historical Chart */}
          <HistoricalChart />

          {/* Strategy Status Alert */}
          {!isStrategyActive && (
            <div className="bg-warning/10 border border-warning/20 rounded-lg p-4">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-warning/10 rounded-full flex items-center justify-center">
                  <div className="w-2 h-2 bg-warning rounded-full" />
                </div>
                <div>
                  <h3 className="font-medium text-warning">Strategy Paused</h3>
                  <p className="text-sm text-warning/80">
                    The Darvas Box strategy is currently paused. No new signals will be generated.
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Capital Constraint Alert */}
          {availableCapital < perTradeLimit && (
            <div className="bg-error/10 border border-error/20 rounded-lg p-4">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-error/10 rounded-full flex items-center justify-center">
                  <div className="w-2 h-2 bg-error rounded-full animate-pulse-trading" />
                </div>
                <div>
                  <h3 className="font-medium text-error">Insufficient Capital</h3>
                  <p className="text-sm text-error/80">
                    Available capital is below minimum trade limit. Strategy will pause automatically.
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>
      </main>
    </div>
  );
};

export default CapitalManagement;