import React, { useState } from 'react';
import { LineChart, Line, BarChart, Bar, PieChart, Pie, Cell, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';

import Button from '../../../components/ui/Button';

const PerformanceCharts = ({ trades }) => {
  const [activeChart, setActiveChart] = useState('monthly');

  const formatCurrency = (value) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(value);
  };

  // Monthly P&L Data
  const getMonthlyData = () => {
    const monthlyData = {};
    trades.forEach(trade => {
      const date = new Date(trade.buyDate);
      const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
      const monthName = date.toLocaleDateString('en-IN', { month: 'short', year: 'numeric' });
      
      if (!monthlyData[monthKey]) {
        monthlyData[monthKey] = {
          month: monthName,
          pnl: 0,
          trades: 0,
          investment: 0
        };
      }
      
      monthlyData[monthKey].pnl += trade.pnl;
      monthlyData[monthKey].trades += 1;
      monthlyData[monthKey].investment += trade.buyPrice * trade.quantity;
    });

    return Object.values(monthlyData).sort((a, b) => a.month.localeCompare(b.month));
  };

  // Stock-wise Performance Data
  const getStockWiseData = () => {
    const stockData = {};
    trades.forEach(trade => {
      if (!stockData[trade.symbol]) {
        stockData[trade.symbol] = {
          symbol: trade.symbol,
          pnl: 0,
          trades: 0,
          investment: 0,
          winRate: 0
        };
      }
      
      stockData[trade.symbol].pnl += trade.pnl;
      stockData[trade.symbol].trades += 1;
      stockData[trade.symbol].investment += trade.buyPrice * trade.quantity;
    });

    // Calculate win rates
    Object.keys(stockData).forEach(symbol => {
      const stockTrades = trades.filter(trade => trade.symbol === symbol && trade.sellDate);
      const winningTrades = stockTrades.filter(trade => trade.pnl > 0);
      stockData[symbol].winRate = stockTrades.length > 0 ? (winningTrades.length / stockTrades.length) * 100 : 0;
    });

    return Object.values(stockData).sort((a, b) => b.pnl - a.pnl);
  };

  // Win/Loss Distribution
  const getWinLossData = () => {
    const completedTrades = trades.filter(trade => trade.sellDate);
    const profitable = completedTrades.filter(trade => trade.pnl > 0).length;
    const losses = completedTrades.filter(trade => trade.pnl < 0).length;
    const breakeven = completedTrades.filter(trade => trade.pnl === 0).length;

    return [
      { name: 'Profitable', value: profitable, color: 'var(--color-success)' },
      { name: 'Loss Making', value: losses, color: 'var(--color-error)' },
      { name: 'Break Even', value: breakeven, color: 'var(--color-muted-foreground)' }
    ].filter(item => item.value > 0);
  };

  const monthlyData = getMonthlyData();
  const stockWiseData = getStockWiseData();
  const winLossData = getWinLossData();

  const chartTabs = [
    { key: 'monthly', label: 'Monthly P&L', icon: 'TrendingUp' },
    { key: 'stocks', label: 'Stock Performance', icon: 'BarChart3' },
    { key: 'distribution', label: 'Win/Loss Distribution', icon: 'PieChart' }
  ];

  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-popover border border-border rounded-lg shadow-trading-lg p-3">
          <p className="text-sm font-medium text-popover-foreground">{label}</p>
          {payload.map((entry, index) => (
            <p key={index} className="text-sm" style={{ color: entry.color }}>
              {entry.name}: {typeof entry.value === 'number' ? formatCurrency(entry.value) : entry.value}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  return (
    <div className="bg-card border border-border rounded-lg p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-foreground">Performance Analytics</h3>
        <div className="flex items-center space-x-1 bg-muted rounded-lg p-1">
          {chartTabs.map((tab) => (
            <Button
              key={tab.key}
              variant={activeChart === tab.key ? "default" : "ghost"}
              size="sm"
              onClick={() => setActiveChart(tab.key)}
              iconName={tab.icon}
              iconPosition="left"
            >
              {tab.label}
            </Button>
          ))}
        </div>
      </div>

      <div className="h-80">
        {activeChart === 'monthly' && (
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={monthlyData}>
              <CartesianGrid strokeDasharray="3 3" stroke="var(--color-border)" />
              <XAxis 
                dataKey="month" 
                stroke="var(--color-muted-foreground)"
                fontSize={12}
              />
              <YAxis 
                stroke="var(--color-muted-foreground)"
                fontSize={12}
                tickFormatter={formatCurrency}
              />
              <Tooltip content={<CustomTooltip />} />
              <Legend />
              <Line 
                type="monotone" 
                dataKey="pnl" 
                stroke="var(--color-primary)" 
                strokeWidth={2}
                dot={{ fill: 'var(--color-primary)', strokeWidth: 2, r: 4 }}
                name="P&L"
              />
            </LineChart>
          </ResponsiveContainer>
        )}

        {activeChart === 'stocks' && (
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={stockWiseData.slice(0, 10)}>
              <CartesianGrid strokeDasharray="3 3" stroke="var(--color-border)" />
              <XAxis 
                dataKey="symbol" 
                stroke="var(--color-muted-foreground)"
                fontSize={12}
              />
              <YAxis 
                stroke="var(--color-muted-foreground)"
                fontSize={12}
                tickFormatter={formatCurrency}
              />
              <Tooltip content={<CustomTooltip />} />
              <Legend />
              <Bar 
                dataKey="pnl" 
                name="P&L"
                fill="var(--color-primary)"
                radius={[4, 4, 0, 0]}
              />
            </BarChart>
          </ResponsiveContainer>
        )}

        {activeChart === 'distribution' && (
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={winLossData}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                outerRadius={120}
                fill="#8884d8"
                dataKey="value"
              >
                {winLossData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip 
                formatter={(value, name) => [value, name]}
                contentStyle={{
                  backgroundColor: 'var(--color-popover)',
                  border: '1px solid var(--color-border)',
                  borderRadius: '8px',
                  color: 'var(--color-popover-foreground)'
                }}
              />
            </PieChart>
          </ResponsiveContainer>
        )}
      </div>

      {/* Chart Summary */}
      <div className="mt-6 pt-6 border-t border-border">
        {activeChart === 'monthly' && (
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
            <div className="text-center">
              <div className="text-lg font-semibold font-data text-foreground">
                {monthlyData.length}
              </div>
              <div className="text-sm text-muted-foreground">Active Months</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-semibold font-data text-success">
                {formatCurrency(Math.max(...monthlyData.map(d => d.pnl), 0))}
              </div>
              <div className="text-sm text-muted-foreground">Best Month</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-semibold font-data text-error">
                {formatCurrency(Math.min(...monthlyData.map(d => d.pnl), 0))}
              </div>
              <div className="text-sm text-muted-foreground">Worst Month</div>
            </div>
          </div>
        )}

        {activeChart === 'stocks' && (
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
            <div className="text-center">
              <div className="text-lg font-semibold font-data text-foreground">
                {stockWiseData.length}
              </div>
              <div className="text-sm text-muted-foreground">Stocks Traded</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-semibold font-data text-success">
                {stockWiseData.length > 0 ? stockWiseData[0].symbol : 'N/A'}
              </div>
              <div className="text-sm text-muted-foreground">Best Performer</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-semibold font-data text-error">
                {stockWiseData.length > 0 ? stockWiseData[stockWiseData.length - 1].symbol : 'N/A'}
              </div>
              <div className="text-sm text-muted-foreground">Worst Performer</div>
            </div>
          </div>
        )}

        {activeChart === 'distribution' && (
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
            {winLossData.map((item, index) => (
              <div key={index} className="text-center">
                <div className="text-lg font-semibold font-data" style={{ color: item.color }}>
                  {item.value}
                </div>
                <div className="text-sm text-muted-foreground">{item.name}</div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default PerformanceCharts;