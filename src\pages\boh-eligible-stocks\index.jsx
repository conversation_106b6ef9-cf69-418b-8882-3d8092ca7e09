import React, { useState, useEffect, useMemo } from 'react';
import Icon from '../../components/AppIcon';
import Button from '../../components/ui/Button';
import StockEligibilityTable from './components/StockEligibilityTable';
import FilterControls from './components/FilterControls';
import ScannerStatus from './components/ScannerStatus';
import MarketInsights from './components/MarketInsights';

const BOHEligibleStocks = () => {
  const [isScanning, setIsScanning] = useState(false);
  const [showInsights, setShowInsights] = useState(false);
  const [filters, setFilters] = useState({
    search: '',
    marketCap: 'all',
    sector: 'all',
    proximity: 'all',
    minPrice: '',
    maxPrice: ''
  });

  // Mock stock data
  const mockStocks = [
    {
      id: 1,
      symbol: 'RELIANCE',
      sector: 'Energy',
      currentPrice: 2456.75,
      weekHigh52: 2456.75,
      weekLow52: 1850.20,
      diffFromHigh: 0,
      dayChange: 2.3,
      marketCap: 'large',
      volume: 1250000
    },
    {
      id: 2,
      symbol: 'TCS',
      sector: 'Information Technology',
      currentPrice: 3234.50,
      weekHigh52: 3250.00,
      weekLow52: 2890.15,
      diffFromHigh: -0.48,
      dayChange: 1.8,
      marketCap: 'large',
      volume: 890000
    },
    {
      id: 3,
      symbol: 'INFY',
      sector: 'Information Technology',
      currentPrice: 1567.25,
      weekHigh52: 1650.00,
      weekLow52: 1350.80,
      diffFromHigh: -5.01,
      dayChange: -0.5,
      marketCap: 'large',
      volume: 1100000
    },
    {
      id: 4,
      symbol: 'HDFC',
      sector: 'Banking',
      currentPrice: 1678.90,
      weekHigh52: 1678.90,
      weekLow52: 1420.30,
      diffFromHigh: 0,
      dayChange: 3.2,
      marketCap: 'large',
      volume: 2100000
    },
    {
      id: 5,
      symbol: 'ICICIBANK',
      sector: 'Banking',
      currentPrice: 945.60,
      weekHigh52: 980.50,
      weekLow52: 780.25,
      diffFromHigh: -3.56,
      dayChange: 1.1,
      marketCap: 'large',
      volume: 1800000
    },
    {
      id: 6,
      symbol: 'WIPRO',
      sector: 'Information Technology',
      currentPrice: 423.80,
      weekHigh52: 445.20,
      weekLow52: 380.15,
      diffFromHigh: -4.81,
      dayChange: 0.8,
      marketCap: 'large',
      volume: 950000
    },
    {
      id: 7,
      symbol: 'MARUTI',
      sector: 'Automobile',
      currentPrice: 9876.45,
      weekHigh52: 9876.45,
      weekLow52: 8200.30,
      diffFromHigh: 0,
      dayChange: 4.1,
      marketCap: 'large',
      volume: 450000
    },
    {
      id: 8,
      symbol: 'HCLTECH',
      sector: 'Information Technology',
      currentPrice: 1234.70,
      weekHigh52: 1290.80,
      weekLow52: 1050.25,
      diffFromHigh: -4.35,
      dayChange: -1.2,
      marketCap: 'large',
      volume: 780000
    },
    {
      id: 9,
      symbol: 'BHARTIARTL',
      sector: 'Telecommunications',
      currentPrice: 856.30,
      weekHigh52: 890.75,
      weekLow52: 720.40,
      diffFromHigh: -3.87,
      dayChange: 2.1,
      marketCap: 'large',
      volume: 1350000
    },
    {
      id: 10,
      symbol: 'SBIN',
      sector: 'Banking',
      currentPrice: 567.85,
      weekHigh52: 590.20,
      weekLow52: 450.80,
      diffFromHigh: -3.78,
      dayChange: 1.5,
      marketCap: 'large',
      volume: 2200000
    },
    {
      id: 11,
      symbol: 'ASIANPAINT',
      sector: 'Chemicals',
      currentPrice: 3456.20,
      weekHigh52: 3456.20,
      weekLow52: 2890.50,
      diffFromHigh: 0,
      dayChange: 1.9,
      marketCap: 'large',
      volume: 320000
    },
    {
      id: 12,
      symbol: 'NESTLEIND',
      sector: 'FMCG',
      currentPrice: 2234.80,
      weekHigh52: 2350.60,
      weekLow52: 1980.30,
      diffFromHigh: -4.92,
      dayChange: 0.7,
      marketCap: 'large',
      volume: 180000
    }
  ];

  // Mock sector data for insights
  const sectorData = [
    { name: 'IT', count: 8 },
    { name: 'Banking', count: 5 },
    { name: 'Energy', count: 3 },
    { name: 'Auto', count: 2 },
    { name: 'FMCG', count: 2 },
    { name: 'Pharma', count: 1 },
    { name: 'Metals', count: 1 },
    { name: 'Telecom', count: 1 }
  ];

  // Mock trend data
  const trendData = [
    { week: 'W1', breakouts: 12 },
    { week: 'W2', breakouts: 18 },
    { week: 'W3', breakouts: 15 },
    { week: 'W4', breakouts: 23 },
    { week: 'W5', breakouts: 19 }
  ];

  // Filter stocks based on current filters
  const filteredStocks = useMemo(() => {
    return mockStocks.filter(stock => {
      // Search filter
      if (filters.search && !stock.symbol.toLowerCase().includes(filters.search.toLowerCase())) {
        return false;
      }

      // Market cap filter
      if (filters.marketCap !== 'all' && stock.marketCap !== filters.marketCap) {
        return false;
      }

      // Sector filter
      if (filters.sector !== 'all') {
        const sectorMap = {
          'banking': 'Banking',
          'it': 'Information Technology',
          'pharma': 'Pharmaceuticals',
          'auto': 'Automobile',
          'fmcg': 'FMCG',
          'metals': 'Metals',
          'energy': 'Energy',
          'realty': 'Real Estate'
        };
        if (stock.sector !== sectorMap[filters.sector]) {
          return false;
        }
      }

      // Proximity filter
      if (filters.proximity !== 'all') {
        switch (filters.proximity) {
          case 'eligible':
            if (stock.diffFromHigh > 0) return false;
            break;
          case 'near':
            if (stock.diffFromHigh > 5 || stock.diffFromHigh <= 0) return false;
            break;
          case 'potential':
            if (stock.diffFromHigh > 10) return false;
            break;
        }
      }

      // Price range filter
      if (filters.minPrice && stock.currentPrice < parseFloat(filters.minPrice)) {
        return false;
      }
      if (filters.maxPrice && stock.currentPrice > parseFloat(filters.maxPrice)) {
        return false;
      }

      return true;
    });
  }, [filters]);

  const eligibleStocks = filteredStocks.filter(stock => stock.diffFromHigh <= 0);

  const handleManualScan = () => {
    setIsScanning(true);
    // Simulate scanning process
    setTimeout(() => {
      setIsScanning(false);
    }, 5000);
  };

  const handleFilterChange = (newFilters) => {
    setFilters(newFilters);
  };

  const handleClearFilters = () => {
    setFilters({
      search: '',
      marketCap: 'all',
      sector: 'all',
      proximity: 'all',
      minPrice: '',
      maxPrice: ''
    });
  };

  const handleExport = () => {
    const csvContent = [
      ['Symbol', 'Sector', 'Current Price', '52W High', '52W Low', '% from High', 'Status'],
      ...filteredStocks.map(stock => [
        stock.symbol,
        stock.sector,
        stock.currentPrice,
        stock.weekHigh52,
        stock.weekLow52,
        stock.diffFromHigh,
        stock.diffFromHigh <= 0 ? 'Eligible' : stock.diffFromHigh <= 5 ? 'Near Eligible' : 'Not Eligible'
      ])
    ].map(row => row.join(',')).join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `boh-eligible-stocks-${new Date().toISOString().split('T')[0]}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
  };

  const handleAddToWatchlist = (stockId) => {
    const stock = mockStocks.find(s => s.id === stockId);
    console.log('Adding to watchlist:', stock?.symbol);
    // Implementation would add to watchlist
  };

  const handleCreateGTT = (stockId) => {
    const stock = mockStocks.find(s => s.id === stockId);
    console.log('Creating GTT for:', stock?.symbol);
    // Implementation would navigate to GTT creation
  };

  return (
    <div className="min-h-screen bg-background pt-16">
      <div className="flex">
        {/* Main Content */}
        <div className="flex-1 p-6">
          <div className="max-w-7xl mx-auto">
            {/* Page Header */}
            <div className="flex items-center justify-between mb-6">
              <div>
                <h1 className="text-2xl font-bold text-foreground">BOH Eligible Stocks</h1>
                <p className="text-muted-foreground mt-1">
                  Identify Breakout of High opportunities for Darvas Box strategy
                </p>
              </div>
              <div className="flex items-center space-x-3">
                <Button
                  variant={showInsights ? "default" : "outline"}
                  onClick={() => setShowInsights(!showInsights)}
                >
                  <Icon name="BarChart3" size={16} />
                  {showInsights ? 'Hide' : 'Show'} Insights
                </Button>
              </div>
            </div>

            <div className="grid grid-cols-1 xl:grid-cols-4 gap-6">
              {/* Main Content Area */}
              <div className={`${showInsights ? 'xl:col-span-3' : 'xl:col-span-4'}`}>
                {/* Scanner Status */}
                <ScannerStatus 
                  onManualScan={handleManualScan}
                  isScanning={isScanning}
                />

                {/* Filter Controls */}
                <FilterControls
                  filters={filters}
                  onFilterChange={handleFilterChange}
                  onClearFilters={handleClearFilters}
                  onExport={handleExport}
                  totalStocks={filteredStocks.length}
                  eligibleStocks={eligibleStocks.length}
                />

                {/* Stock Table */}
                <StockEligibilityTable
                  stocks={filteredStocks}
                  onAddToWatchlist={handleAddToWatchlist}
                  onCreateGTT={handleCreateGTT}
                />
              </div>

              {/* Market Insights Sidebar */}
              {showInsights && (
                <div className="xl:col-span-1">
                  <MarketInsights 
                    sectorData={sectorData}
                    trendData={trendData}
                  />
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BOHEligibleStocks;