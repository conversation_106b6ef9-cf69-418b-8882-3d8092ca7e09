import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import tradingService from '../../utils/tradingService';
import Icon from '../../components/AppIcon';
import Button from '../../components/ui/Button';
import KPICard from './components/KPICard';
import StrategyToggle from './components/StrategyToggle';
import HoldingsTable from './components/HoldingsTable';
import RecentActivity from './components/RecentActivity';
import UpcomingGTT from './components/UpcomingGTT';
import QuickActions from './components/QuickActions';

const Dashboard = () => {
  const { user, userProfile } = useAuth();
  const [isStrategyActive, setIsStrategyActive] = useState(true);
  const [lastUpdate, setLastUpdate] = useState(new Date());
  const [loading, setLoading] = useState(true);

  // State for real data
  const [kpiData, setKpiData] = useState({
    totalCapital: 0,
    usedCapital: 0,
    availableCapital: 0,
    currentPnL: 0
  });

  const [holdings, setHoldings] = useState([]);
  const [recentActivities, setRecentActivities] = useState([]);
  const [gttOrders, setGttOrders] = useState([]);

  // Load dashboard data
  useEffect(() => {
    let isMounted = true;

    const loadDashboardData = async () => {
      if (!user?.id) return;

      try {
        setLoading(true);

        // Load capital data
        const capitalResult = await tradingService.getCapitalTracker(user.id);
        if (capitalResult?.success && isMounted) {
          const capital = capitalResult.data;
          setKpiData({
            totalCapital: parseFloat(capital.total_capital) || 0,
            usedCapital: parseFloat(capital.used_capital) || 0,
            availableCapital: parseFloat(capital.available_capital) || 0,
            currentPnL: 0 // Will be calculated from holdings
          });
          setIsStrategyActive(capital.strategy_status === 'active');
        }

        // Load holdings
        const holdingsResult = await tradingService.getCurrentHoldings(user.id);
        if (holdingsResult?.success && isMounted) {
          setHoldings(holdingsResult.data || []);
        }

        // Load GTT orders
        const gttResult = await tradingService.getGTTOrders(user.id, { status: 'pending' });
        if (gttResult?.success && isMounted) {
          setGttOrders(gttResult.data || []);
        }

        // Load recent trades for activity
        const tradesResult = await tradingService.getTradeLog(user.id, {});
        if (tradesResult?.success && isMounted) {
          const activities = tradesResult.data?.slice(0, 5)?.map(trade => ({
            type: trade.trade_type,
            title: `${trade.trade_type === 'buy' ? 'Bought' : 'Sold'} ${trade.symbol}`,
            description: `${trade.quantity} shares at ₹${trade.price} ${trade.entry_number ? `(Entry ${trade.entry_number})` : ''}`,
            timestamp: new Date(trade.trade_date),
            amount: parseFloat(trade.amount),
            quantity: trade.quantity
          })) || [];
          setRecentActivities(activities);
        }

      } catch (error) {
        console.log('Dashboard data loading error:', error);
      } finally {
        if (isMounted) {
          setLoading(false);
          setLastUpdate(new Date());
        }
      }
    };

    loadDashboardData();

    // Set up real-time subscriptions
    const holdingsSubscription = tradingService.subscribeToHoldings(user?.id, (payload) => {
      if (payload.eventType === 'UPDATE' || payload.eventType === 'INSERT') {
        loadDashboardData(); // Reload all data when holdings change
      }
    });

    const gttSubscription = tradingService.subscribeToGTTOrders(user?.id, (payload) => {
      if (payload.eventType === 'UPDATE' || payload.eventType === 'INSERT') {
        loadDashboardData(); // Reload GTT orders
      }
    });

    // Auto-refresh every 30 seconds
    const interval = setInterval(() => {
      loadDashboardData();
    }, 30000);

    return () => {
      isMounted = false;
      if (holdingsSubscription) {
        holdingsSubscription.unsubscribe();
      }
      if (gttSubscription) {
        gttSubscription.unsubscribe();
      }
      clearInterval(interval);
    };
  }, [user?.id]);

  const handleStrategyToggle = async (newState) => {
    if (!user?.id) return;

    try {
      const status = newState ? 'active' : 'paused';
      const result = await tradingService.updateStrategyStatus(user.id, status);
      
      if (result?.success) {
        setIsStrategyActive(newState);
      }
    } catch (error) {
      console.log('Strategy toggle error:', error);
    }
  };

  const handleManualSignal = async () => {
    console.log('Generating manual signal...');
    // This would trigger the weekly signal generation manually
    // For now, just simulate a delay
    await new Promise(resolve => setTimeout(resolve, 2000));
  };

  const handleEmergencyPause = async () => {
    await handleStrategyToggle(false);
  };

  const handleRefreshData = async () => {
    setLastUpdate(new Date());
    // Trigger data reload
    window.location.reload();
  };

  const calculateUsagePercentage = () => {
    if (kpiData.totalCapital === 0) return 0;
    return (kpiData.usedCapital / kpiData.totalCapital) * 100;
  };

  const calculateCurrentPnL = () => {
    if (!holdings?.length) return 0;
    
    return holdings.reduce((total, holding) => {
      const currentValue = (holding.current_price || 0) * holding.total_quantity;
      const invested = holding.total_invested || 0;
      return total + (currentValue - invested);
    }, 0);
  };

  const formatTime = (date) => {
    return date.toLocaleTimeString('en-IN', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-background pt-16 flex items-center justify-center">
        <div className="flex flex-col items-center space-y-4">
          <div className="w-8 h-8 border-4 border-primary/30 border-t-primary rounded-full animate-spin" />
          <p className="text-muted-foreground">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background pt-16">
      {/* Header with live status */}
      <div className="bg-card border-b border-border">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-foreground">Trading Dashboard</h1>
              <p className="text-muted-foreground">
                Monitor your Darvas Box strategy performance and portfolio
              </p>
              {userProfile && (
                <p className="text-sm text-muted-foreground mt-1">
                  Welcome back, {userProfile.full_name} ({userProfile.role})
                </p>
              )}
            </div>
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2 text-sm">
                <div className="w-2 h-2 bg-success rounded-full animate-pulse-trading" />
                <span className="text-muted-foreground">Live</span>
                <span className="text-muted-foreground">•</span>
                <span className="text-muted-foreground font-data">
                  Updated {formatTime(lastUpdate)}
                </span>
              </div>
              <Button variant="outline" size="sm" onClick={handleRefreshData}>
                <Icon name="RefreshCw" size={16} />
                Refresh
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-6 py-8">
        {/* KPI Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <KPICard
            title="Total Capital"
            value={kpiData.totalCapital}
            icon="Wallet"
            color="primary"
          />
          <KPICard
            title="Used Capital"
            value={kpiData.usedCapital}
            change={calculateUsagePercentage()}
            changeType="neutral"
            icon="TrendingUp"
            color={calculateUsagePercentage() > 80 ? "warning" : "success"}
          />
          <KPICard
            title="Available Capital"
            value={kpiData.availableCapital}
            icon="PiggyBank"
            color="success"
          />
          <KPICard
            title="Current P&L"
            value={calculateCurrentPnL()}
            change={calculateCurrentPnL() >= 0 ? 12.5 : -8.3}
            changeType={calculateCurrentPnL() >= 0 ? "positive" : "negative"}
            icon={calculateCurrentPnL() >= 0 ? "TrendingUp" : "TrendingDown"}
            color={calculateCurrentPnL() >= 0 ? "success" : "error"}
          />
        </div>

        {/* Strategy Toggle */}
        <div className="mb-8">
          <StrategyToggle
            isActive={isStrategyActive}
            onToggle={handleStrategyToggle}
            lastSignalTime={new Date(Date.now() - ********)}
            nextSignalTime={new Date(Date.now() + *********)}
          />
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Holdings Table - Takes 2 columns */}
          <div className="lg:col-span-2">
            <HoldingsTable holdings={holdings} />
          </div>

          {/* Right Sidebar */}
          <div className="space-y-8">
            <RecentActivity activities={recentActivities} />
            <UpcomingGTT gttOrders={gttOrders} />
            <QuickActions
              onManualSignal={handleManualSignal}
              onEmergencyPause={handleEmergencyPause}
              onRefreshData={handleRefreshData}
            />
          </div>
        </div>

        {/* Bottom Navigation Links */}
        <div className="mt-12 grid grid-cols-1 md:grid-cols-3 gap-6">
          <Link
            to="/gtt-orders-management"
            className="bg-card border border-border rounded-lg p-6 hover:bg-muted/30 transition-colors group"
          >
            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center group-hover:bg-primary/20 transition-colors">
                <Icon name="TrendingUp" size={24} color="var(--color-primary)" />
              </div>
              <div>
                <h3 className="font-semibold text-foreground">GTT Orders</h3>
                <p className="text-sm text-muted-foreground">
                  Manage buy and sell triggers
                </p>
              </div>
            </div>
          </Link>

          <Link
            to="/trade-log"
            className="bg-card border border-border rounded-lg p-6 hover:bg-muted/30 transition-colors group"
          >
            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 bg-accent/10 rounded-lg flex items-center justify-center group-hover:bg-accent/20 transition-colors">
                <Icon name="PieChart" size={24} color="var(--color-accent)" />
              </div>
              <div>
                <h3 className="font-semibold text-foreground">Trade Analysis</h3>
                <p className="text-sm text-muted-foreground">
                  View detailed trade history
                </p>
              </div>
            </div>
          </Link>

          <Link
            to="/capital-management"
            className="bg-card border border-border rounded-lg p-6 hover:bg-muted/30 transition-colors group"
          >
            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 bg-success/10 rounded-lg flex items-center justify-center group-hover:bg-success/20 transition-colors">
                <Icon name="Settings" size={24} color="var(--color-success)" />
              </div>
              <div>
                <h3 className="font-semibold text-foreground">Settings</h3>
                <p className="text-sm text-muted-foreground">
                  Configure capital and limits
                </p>
              </div>
            </div>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;