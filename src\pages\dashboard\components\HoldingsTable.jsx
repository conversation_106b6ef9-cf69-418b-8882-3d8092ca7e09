import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const HoldingsTable = ({ holdings }) => {
  const [expandedRows, setExpandedRows] = useState(new Set());

  const toggleRow = (stockId) => {
    const newExpanded = new Set(expandedRows);
    if (newExpanded.has(stockId)) {
      newExpanded.delete(stockId);
    } else {
      newExpanded.add(stockId);
    }
    setExpandedRows(newExpanded);
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const formatPrice = (price) => {
    return new Intl.NumberFormat('en-IN', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(price);
  };

  const calculatePnLPercentage = (currentPrice, avgPrice) => {
    return ((currentPrice - avgPrice) / avgPrice) * 100;
  };

  if (!holdings || holdings.length === 0) {
    return (
      <div className="bg-card border border-border rounded-lg p-8 shadow-trading">
        <div className="text-center">
          <Icon name="TrendingUp" size={48} color="var(--color-muted-foreground)" className="mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-foreground mb-2">No Holdings Yet</h3>
          <p className="text-muted-foreground mb-4">
            Your portfolio will appear here once you start trading with the Darvas strategy.
          </p>
          <Button variant="outline" iconName="Plus" iconPosition="left">
            Generate Test Signal
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-card border border-border rounded-lg shadow-trading overflow-hidden">
      <div className="p-6 border-b border-border">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-foreground">Current Holdings</h3>
            <p className="text-sm text-muted-foreground">
              {holdings.length} stocks • Click to expand details
            </p>
          </div>
          <Button variant="outline" size="sm" iconName="RefreshCw" iconPosition="left">
            Refresh Prices
          </Button>
        </div>
      </div>

      <div className="overflow-x-auto">
        <table className="w-full">
          <thead className="bg-muted/50">
            <tr>
              <th className="text-left p-4 text-sm font-medium text-muted-foreground">Stock</th>
              <th className="text-right p-4 text-sm font-medium text-muted-foreground">Quantity</th>
              <th className="text-right p-4 text-sm font-medium text-muted-foreground">Avg Price</th>
              <th className="text-right p-4 text-sm font-medium text-muted-foreground">Current Price</th>
              <th className="text-right p-4 text-sm font-medium text-muted-foreground">Invested</th>
              <th className="text-right p-4 text-sm font-medium text-muted-foreground">Current Value</th>
              <th className="text-right p-4 text-sm font-medium text-muted-foreground">P&L</th>
              <th className="w-12 p-4"></th>
            </tr>
          </thead>
          <tbody>
            {holdings.map((holding) => {
              const isExpanded = expandedRows.has(holding.id);
              const pnlAmount = (holding.currentPrice - holding.avgPrice) * holding.quantity;
              const pnlPercentage = calculatePnLPercentage(holding.currentPrice, holding.avgPrice);
              const currentValue = holding.currentPrice * holding.quantity;
              
              return (
                <React.Fragment key={holding.id}>
                  <tr className="border-b border-border hover:bg-muted/30 transition-colors">
                    <td className="p-4">
                      <div className="flex items-center space-x-3">
                        <div className="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center">
                          <span className="text-xs font-bold text-primary">
                            {holding.symbol.substring(0, 2)}
                          </span>
                        </div>
                        <div>
                          <div className="font-medium text-foreground">{holding.symbol}</div>
                          <div className="text-xs text-muted-foreground">{holding.name}</div>
                        </div>
                      </div>
                    </td>
                    <td className="p-4 text-right font-data text-foreground">{holding.quantity}</td>
                    <td className="p-4 text-right font-data text-foreground">₹{formatPrice(holding.avgPrice)}</td>
                    <td className="p-4 text-right font-data text-foreground">₹{formatPrice(holding.currentPrice)}</td>
                    <td className="p-4 text-right font-data text-foreground">{formatCurrency(holding.invested)}</td>
                    <td className="p-4 text-right font-data text-foreground">{formatCurrency(currentValue)}</td>
                    <td className="p-4 text-right">
                      <div className={`font-data ${pnlAmount >= 0 ? 'text-success' : 'text-error'}`}>
                        {formatCurrency(Math.abs(pnlAmount))}
                      </div>
                      <div className={`text-xs ${pnlAmount >= 0 ? 'text-success' : 'text-error'}`}>
                        {pnlAmount >= 0 ? '+' : '-'}{Math.abs(pnlPercentage).toFixed(2)}%
                      </div>
                    </td>
                    <td className="p-4">
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => toggleRow(holding.id)}
                      >
                        <Icon 
                          name={isExpanded ? "ChevronUp" : "ChevronDown"} 
                          size={16} 
                        />
                      </Button>
                    </td>
                  </tr>
                  
                  {isExpanded && (
                    <tr className="bg-muted/20">
                      <td colSpan="8" className="p-0">
                        <div className="p-6 space-y-4">
                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div className="bg-card rounded-lg p-4 border border-border">
                              <h4 className="text-sm font-medium text-muted-foreground mb-3">Entry Details</h4>
                              <div className="space-y-2">
                                {holding.entries.map((entry, index) => (
                                  <div key={index} className="flex justify-between items-center text-sm">
                                    <span className="text-muted-foreground">
                                      Entry {index + 1} ({entry.quantity} shares)
                                    </span>
                                    <span className="font-data text-foreground">₹{formatPrice(entry.price)}</span>
                                  </div>
                                ))}
                              </div>
                            </div>
                            
                            <div className="bg-card rounded-lg p-4 border border-border">
                              <h4 className="text-sm font-medium text-muted-foreground mb-3">Target & Limits</h4>
                              <div className="space-y-2 text-sm">
                                <div className="flex justify-between">
                                  <span className="text-muted-foreground">Target Price</span>
                                  <span className="font-data text-success">₹{formatPrice(holding.targetPrice)}</span>
                                </div>
                                <div className="flex justify-between">
                                  <span className="text-muted-foreground">Capital Used</span>
                                  <span className="font-data text-foreground">{formatCurrency(holding.invested)}</span>
                                </div>
                                <div className="flex justify-between">
                                  <span className="text-muted-foreground">Remaining Limit</span>
                                  <span className="font-data text-muted-foreground">
                                    {formatCurrency(10000 - holding.invested)}
                                  </span>
                                </div>
                              </div>
                            </div>
                            
                            <div className="bg-card rounded-lg p-4 border border-border">
                              <h4 className="text-sm font-medium text-muted-foreground mb-3">Performance</h4>
                              <div className="space-y-2 text-sm">
                                <div className="flex justify-between">
                                  <span className="text-muted-foreground">Days Held</span>
                                  <span className="font-data text-foreground">{holding.daysHeld}</span>
                                </div>
                                <div className="flex justify-between">
                                  <span className="text-muted-foreground">52W High</span>
                                  <span className="font-data text-foreground">₹{formatPrice(holding.weekHigh52)}</span>
                                </div>
                                <div className="flex justify-between">
                                  <span className="text-muted-foreground">Distance to Target</span>
                                  <span className={`font-data ${
                                    holding.currentPrice >= holding.targetPrice ? 'text-success' : 'text-warning'
                                  }`}>
                                    {((holding.targetPrice - holding.currentPrice) / holding.currentPrice * 100).toFixed(2)}%
                                  </span>
                                </div>
                              </div>
                            </div>
                          </div>
                          
                          <div className="flex justify-end space-x-2">
                            <Button variant="outline" size="sm" iconName="TrendingUp">
                              View Chart
                            </Button>
                            <Button variant="outline" size="sm" iconName="Edit">
                              Modify GTT
                            </Button>
                            <Button variant="destructive" size="sm" iconName="X">
                              Exit Position
                            </Button>
                          </div>
                        </div>
                      </td>
                    </tr>
                  )}
                </React.Fragment>
              );
            })}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default HoldingsTable;