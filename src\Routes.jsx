import React from "react";
import { BrowserRouter, Routes as RouterRoutes, Route } from "react-router-dom";
import ScrollToTop from "components/ScrollToTop";
import ErrorBoundary from "components/ErrorBoundary";
import { useAuth } from "./contexts/AuthContext";
import NotFound from "pages/NotFound";
import AuthModal from "./components/auth/AuthModal";
import GTTOrdersManagement from './pages/gtt-orders-management';
import Dashboard from './pages/dashboard';
import BOHEligibleStocks from './pages/boh-eligible-stocks';
import CapitalManagement from './pages/capital-management';
import TradeLog from './pages/trade-log';
import SettingsTesting from './pages/settings-testing';

const Routes = () => {
  const { user, loading } = useAuth();

  // Show loading spinner while checking auth
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="flex flex-col items-center space-y-4">
          <div className="w-8 h-8 border-4 border-primary/30 border-t-primary rounded-full animate-spin" />
          <p className="text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  // Show auth modal if not authenticated
  if (!user) {
    return <AuthModal />;
  }

  return (
    <BrowserRouter>
      <ErrorBoundary>
      <ScrollToTop />
      <RouterRoutes>
        {/* Define your route here */}
        <Route path="/" element={<Dashboard />} />
        <Route path="/dashboard" element={<Dashboard />} />
        <Route path="/gtt-orders-management" element={<GTTOrdersManagement />} />
        <Route path="/boh-eligible-stocks" element={<BOHEligibleStocks />} />
        <Route path="/capital-management" element={<CapitalManagement />} />
        <Route path="/trade-log" element={<TradeLog />} />
        <Route path="/settings-testing" element={<SettingsTesting />} />
        <Route path="*" element={<NotFound />} />
      </RouterRoutes>
      </ErrorBoundary>
    </BrowserRouter>
  );
};

export default Routes;