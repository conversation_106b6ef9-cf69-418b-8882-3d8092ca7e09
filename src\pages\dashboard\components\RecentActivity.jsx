import React from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const RecentActivity = ({ activities }) => {
  const formatTime = (date) => {
    return date.toLocaleTimeString('en-IN', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const getActivityIcon = (type) => {
    switch (type) {
      case 'buy': return 'TrendingUp';
      case 'sell': return 'TrendingDown';
      case 'gtt_created': return 'Plus';
      case 'gtt_triggered': return 'Zap';
      case 'signal_generated': return 'Bell';
      default: return 'Activity';
    }
  };

  const getActivityColor = (type) => {
    switch (type) {
      case 'buy': return 'text-success';
      case 'sell': return 'text-error';
      case 'gtt_created': return 'text-accent';
      case 'gtt_triggered': return 'text-warning';
      case 'signal_generated': return 'text-primary';
      default: return 'text-muted-foreground';
    }
  };

  const getActivityBg = (type) => {
    switch (type) {
      case 'buy': return 'bg-success/10';
      case 'sell': return 'bg-error/10';
      case 'gtt_created': return 'bg-accent/10';
      case 'gtt_triggered': return 'bg-warning/10';
      case 'signal_generated': return 'bg-primary/10';
      default: return 'bg-muted/10';
    }
  };

  return (
    <div className="bg-card border border-border rounded-lg shadow-trading">
      <div className="p-6 border-b border-border">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-foreground">Recent Activity</h3>
            <p className="text-sm text-muted-foreground">Latest trading actions and signals</p>
          </div>
          <Button variant="outline" size="sm" iconName="ExternalLink">
            View All
          </Button>
        </div>
      </div>

      <div className="p-6">
        {activities && activities.length > 0 ? (
          <div className="space-y-4">
            {activities.map((activity, index) => (
              <div key={index} className="flex items-start space-x-4">
                <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${getActivityBg(activity.type)}`}>
                  <Icon 
                    name={getActivityIcon(activity.type)} 
                    size={16} 
                    className={getActivityColor(activity.type)}
                  />
                </div>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <p className="text-sm font-medium text-foreground truncate">
                      {activity.title}
                    </p>
                    <span className="text-xs text-muted-foreground font-data">
                      {formatTime(activity.timestamp)}
                    </span>
                  </div>
                  
                  <p className="text-sm text-muted-foreground mt-1">
                    {activity.description}
                  </p>
                  
                  {activity.amount && (
                    <div className="flex items-center space-x-2 mt-2">
                      <span className={`text-sm font-data font-medium ${
                        activity.type === 'buy' ? 'text-error' : 
                        activity.type === 'sell' ? 'text-success' : 'text-foreground'
                      }`}>
                        {activity.type === 'buy' ? '-' : activity.type === 'sell' ? '+' : ''}
                        {formatCurrency(activity.amount)}
                      </span>
                      {activity.quantity && (
                        <span className="text-xs text-muted-foreground">
                          ({activity.quantity} shares)
                        </span>
                      )}
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8">
            <Icon name="Activity" size={48} color="var(--color-muted-foreground)" className="mx-auto mb-4" />
            <h4 className="text-lg font-medium text-foreground mb-2">No Recent Activity</h4>
            <p className="text-muted-foreground">
              Trading activities will appear here once you start using the strategy.
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default RecentActivity;