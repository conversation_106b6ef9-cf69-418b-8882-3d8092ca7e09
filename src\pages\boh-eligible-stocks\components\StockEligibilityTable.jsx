import React, { useState, useMemo } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const StockEligibilityTable = ({ stocks, onAddToWatchlist, onCreateGTT }) => {
  const [sortConfig, setSortConfig] = useState({ key: 'symbol', direction: 'asc' });
  const [selectedStocks, setSelectedStocks] = useState([]);

  const sortedStocks = useMemo(() => {
    const sortableStocks = [...stocks];
    if (sortConfig.key) {
      sortableStocks.sort((a, b) => {
        if (a[sortConfig.key] < b[sortConfig.key]) {
          return sortConfig.direction === 'asc' ? -1 : 1;
        }
        if (a[sortConfig.key] > b[sortConfig.key]) {
          return sortConfig.direction === 'asc' ? 1 : -1;
        }
        return 0;
      });
    }
    return sortableStocks;
  }, [stocks, sortConfig]);

  const handleSort = (key) => {
    let direction = 'asc';
    if (sortConfig.key === key && sortConfig.direction === 'asc') {
      direction = 'desc';
    }
    setSortConfig({ key, direction });
  };

  const handleSelectStock = (stockId) => {
    setSelectedStocks(prev => 
      prev.includes(stockId) 
        ? prev.filter(id => id !== stockId)
        : [...prev, stockId]
    );
  };

  const handleSelectAll = () => {
    if (selectedStocks.length === stocks.length) {
      setSelectedStocks([]);
    } else {
      setSelectedStocks(stocks.map(stock => stock.id));
    }
  };

  const getEligibilityBadge = (stock) => {
    const diffFromHigh = stock.diffFromHigh;
    if (diffFromHigh <= 0) {
      return (
        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-success/10 text-success">
          <Icon name="TrendingUp" size={12} className="mr-1" />
          Eligible
        </span>
      );
    } else if (diffFromHigh <= 5) {
      return (
        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-warning/10 text-warning">
          <Icon name="Clock" size={12} className="mr-1" />
          Near Eligible
        </span>
      );
    } else {
      return (
        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-muted text-muted-foreground">
          <Icon name="Minus" size={12} className="mr-1" />
          Not Eligible
        </span>
      );
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount);
  };

  const formatPercentage = (value) => {
    const sign = value >= 0 ? '+' : '';
    return `${sign}${value.toFixed(2)}%`;
  };

  return (
    <div className="bg-card rounded-lg border border-border overflow-hidden">
      {/* Table Header Actions */}
      <div className="p-4 border-b border-border bg-muted/30">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={selectedStocks.length === stocks.length}
                onChange={handleSelectAll}
                className="w-4 h-4 text-primary bg-background border-border rounded focus:ring-primary focus:ring-2"
              />
              <span className="text-sm text-muted-foreground">
                {selectedStocks.length > 0 ? `${selectedStocks.length} selected` : 'Select all'}
              </span>
            </div>
            {selectedStocks.length > 0 && (
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => selectedStocks.forEach(id => onAddToWatchlist(id))}
                >
                  <Icon name="Star" size={14} />
                  Add to Watchlist
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => selectedStocks.forEach(id => onCreateGTT(id))}
                >
                  <Icon name="Plus" size={14} />
                  Create GTT
                </Button>
              </div>
            )}
          </div>
          <div className="text-sm text-muted-foreground">
            {stocks.length} stocks • {stocks.filter(s => s.diffFromHigh <= 0).length} eligible
          </div>
        </div>
      </div>

      {/* Table */}
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead className="bg-muted/50">
            <tr>
              <th className="w-12 px-4 py-3 text-left">
                <span className="sr-only">Select</span>
              </th>
              <th 
                className="px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wide cursor-pointer hover:text-foreground transition-colors"
                onClick={() => handleSort('symbol')}
              >
                <div className="flex items-center space-x-1">
                  <span>Symbol</span>
                  <Icon 
                    name={sortConfig.key === 'symbol' ? (sortConfig.direction === 'asc' ? 'ChevronUp' : 'ChevronDown') : 'ChevronsUpDown'} 
                    size={14} 
                  />
                </div>
              </th>
              <th 
                className="px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wide cursor-pointer hover:text-foreground transition-colors"
                onClick={() => handleSort('currentPrice')}
              >
                <div className="flex items-center space-x-1">
                  <span>Current Price</span>
                  <Icon 
                    name={sortConfig.key === 'currentPrice' ? (sortConfig.direction === 'asc' ? 'ChevronUp' : 'ChevronDown') : 'ChevronsUpDown'} 
                    size={14} 
                  />
                </div>
              </th>
              <th 
                className="px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wide cursor-pointer hover:text-foreground transition-colors"
                onClick={() => handleSort('weekHigh52')}
              >
                <div className="flex items-center space-x-1">
                  <span>52W High</span>
                  <Icon 
                    name={sortConfig.key === 'weekHigh52' ? (sortConfig.direction === 'asc' ? 'ChevronUp' : 'ChevronDown') : 'ChevronsUpDown'} 
                    size={14} 
                  />
                </div>
              </th>
              <th 
                className="px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wide cursor-pointer hover:text-foreground transition-colors"
                onClick={() => handleSort('weekLow52')}
              >
                <div className="flex items-center space-x-1">
                  <span>52W Low</span>
                  <Icon 
                    name={sortConfig.key === 'weekLow52' ? (sortConfig.direction === 'asc' ? 'ChevronUp' : 'ChevronDown') : 'ChevronsUpDown'} 
                    size={14} 
                  />
                </div>
              </th>
              <th 
                className="px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wide cursor-pointer hover:text-foreground transition-colors"
                onClick={() => handleSort('diffFromHigh')}
              >
                <div className="flex items-center space-x-1">
                  <span>% from High</span>
                  <Icon 
                    name={sortConfig.key === 'diffFromHigh' ? (sortConfig.direction === 'asc' ? 'ChevronUp' : 'ChevronDown') : 'ChevronsUpDown'} 
                    size={14} 
                  />
                </div>
              </th>
              <th className="px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wide">
                Status
              </th>
              <th className="px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wide">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="divide-y divide-border">
            {sortedStocks.map((stock) => (
              <tr key={stock.id} className="hover:bg-muted/30 transition-colors">
                <td className="px-4 py-4">
                  <input
                    type="checkbox"
                    checked={selectedStocks.includes(stock.id)}
                    onChange={() => handleSelectStock(stock.id)}
                    className="w-4 h-4 text-primary bg-background border-border rounded focus:ring-primary focus:ring-2"
                  />
                </td>
                <td className="px-4 py-4">
                  <div className="flex flex-col">
                    <span className="text-sm font-medium text-foreground">{stock.symbol}</span>
                    <span className="text-xs text-muted-foreground">{stock.sector}</span>
                  </div>
                </td>
                <td className="px-4 py-4">
                  <div className="flex flex-col">
                    <span className="text-sm font-data font-medium text-foreground">
                      {formatCurrency(stock.currentPrice)}
                    </span>
                    <span className={`text-xs font-data ${
                      stock.dayChange >= 0 ? 'text-success' : 'text-error'
                    }`}>
                      {formatPercentage(stock.dayChange)}
                    </span>
                  </div>
                </td>
                <td className="px-4 py-4">
                  <span className="text-sm font-data text-foreground">
                    {formatCurrency(stock.weekHigh52)}
                  </span>
                </td>
                <td className="px-4 py-4">
                  <span className="text-sm font-data text-foreground">
                    {formatCurrency(stock.weekLow52)}
                  </span>
                </td>
                <td className="px-4 py-4">
                  <span className={`text-sm font-data font-medium ${
                    stock.diffFromHigh <= 0 ? 'text-success' : 
                    stock.diffFromHigh <= 5 ? 'text-warning' : 'text-muted-foreground'
                  }`}>
                    {formatPercentage(stock.diffFromHigh)}
                  </span>
                </td>
                <td className="px-4 py-4">
                  {getEligibilityBadge(stock)}
                </td>
                <td className="px-4 py-4">
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onAddToWatchlist(stock.id)}
                      title="Add to Watchlist"
                    >
                      <Icon name="Star" size={14} />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onCreateGTT(stock.id)}
                      title="Create GTT Order"
                    >
                      <Icon name="Plus" size={14} />
                    </Button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {stocks.length === 0 && (
        <div className="p-8 text-center">
          <Icon name="Search" size={48} color="var(--color-muted-foreground)" className="mx-auto mb-4" />
          <h3 className="text-lg font-medium text-foreground mb-2">No stocks found</h3>
          <p className="text-sm text-muted-foreground">
            Try adjusting your filters or run a new scan to find eligible stocks.
          </p>
        </div>
      )}
    </div>
  );
};

export default StockEligibilityTable;