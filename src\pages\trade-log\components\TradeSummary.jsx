import React from 'react';
import Icon from '../../../components/AppIcon';

const TradeSummary = ({ trades }) => {
  const calculateSummary = () => {
    const totalTrades = trades.length;
    const completedTrades = trades.filter(trade => trade.sellDate);
    const activeTrades = trades.filter(trade => !trade.sellDate);
    
    const totalInvestment = trades.reduce((sum, trade) => sum + (trade.buyPrice * trade.quantity), 0);
    const totalPnL = trades.reduce((sum, trade) => sum + trade.pnl, 0);
    
    const profitableTrades = completedTrades.filter(trade => trade.pnl > 0);
    const lossMakingTrades = completedTrades.filter(trade => trade.pnl < 0);
    const breakEvenTrades = completedTrades.filter(trade => trade.pnl === 0);
    
    const winRate = completedTrades.length > 0 ? (profitableTrades.length / completedTrades.length) * 100 : 0;
    const avgProfit = profitableTrades.length > 0 ? profitableTrades.reduce((sum, trade) => sum + trade.pnl, 0) / profitableTrades.length : 0;
    const avgLoss = lossMakingTrades.length > 0 ? lossMakingTrades.reduce((sum, trade) => sum + trade.pnl, 0) / lossMakingTrades.length : 0;
    
    const totalPnLPercent = totalInvestment > 0 ? (totalPnL / totalInvestment) * 100 : 0;
    
    return {
      totalTrades,
      completedTrades: completedTrades.length,
      activeTrades: activeTrades.length,
      totalInvestment,
      totalPnL,
      totalPnLPercent,
      profitableTrades: profitableTrades.length,
      lossMakingTrades: lossMakingTrades.length,
      breakEvenTrades: breakEvenTrades.length,
      winRate,
      avgProfit,
      avgLoss
    };
  };

  const summary = calculateSummary();

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const summaryCards = [
    {
      title: 'Total Trades',
      value: summary.totalTrades,
      icon: 'BarChart3',
      color: 'text-accent',
      bgColor: 'bg-accent/10'
    },
    {
      title: 'Active Trades',
      value: summary.activeTrades,
      icon: 'Activity',
      color: 'text-warning',
      bgColor: 'bg-warning/10'
    },
    {
      title: 'Completed Trades',
      value: summary.completedTrades,
      icon: 'CheckCircle',
      color: 'text-success',
      bgColor: 'bg-success/10'
    },
    {
      title: 'Win Rate',
      value: `${summary.winRate.toFixed(1)}%`,
      icon: 'Target',
      color: summary.winRate >= 60 ? 'text-success' : summary.winRate >= 40 ? 'text-warning' : 'text-error',
      bgColor: summary.winRate >= 60 ? 'bg-success/10' : summary.winRate >= 40 ? 'bg-warning/10' : 'bg-error/10'
    },
    {
      title: 'Total Investment',
      value: formatCurrency(summary.totalInvestment),
      icon: 'DollarSign',
      color: 'text-primary',
      bgColor: 'bg-primary/10'
    },
    {
      title: 'Total P&L',
      value: formatCurrency(summary.totalPnL),
      icon: summary.totalPnL >= 0 ? 'TrendingUp' : 'TrendingDown',
      color: summary.totalPnL >= 0 ? 'text-success' : 'text-error',
      bgColor: summary.totalPnL >= 0 ? 'bg-success/10' : 'bg-error/10'
    }
  ];

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
        {summaryCards.map((card, index) => (
          <div key={index} className="bg-card border border-border rounded-lg p-4">
            <div className="flex items-center space-x-3">
              <div className={`w-10 h-10 rounded-lg ${card.bgColor} flex items-center justify-center`}>
                <Icon name={card.icon} size={20} color={`var(--color-${card.color.split('-')[1]})`} />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">{card.title}</p>
                <p className={`text-lg font-semibold font-data ${card.color}`}>
                  {card.value}
                </p>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Detailed Summary */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Performance Breakdown */}
        <div className="bg-card border border-border rounded-lg p-6">
          <h3 className="text-lg font-semibold text-foreground mb-4">Performance Breakdown</h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between p-3 bg-success/5 rounded-lg">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-success/10 rounded-full flex items-center justify-center">
                  <Icon name="TrendingUp" size={16} color="var(--color-success)" />
                </div>
                <span className="text-sm font-medium">Profitable Trades</span>
              </div>
              <div className="text-right">
                <div className="text-sm font-data font-semibold text-success">
                  {summary.profitableTrades}
                </div>
                <div className="text-xs text-muted-foreground">
                  Avg: {formatCurrency(summary.avgProfit)}
                </div>
              </div>
            </div>

            <div className="flex items-center justify-between p-3 bg-error/5 rounded-lg">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-error/10 rounded-full flex items-center justify-center">
                  <Icon name="TrendingDown" size={16} color="var(--color-error)" />
                </div>
                <span className="text-sm font-medium">Loss Making Trades</span>
              </div>
              <div className="text-right">
                <div className="text-sm font-data font-semibold text-error">
                  {summary.lossMakingTrades}
                </div>
                <div className="text-xs text-muted-foreground">
                  Avg: {formatCurrency(summary.avgLoss)}
                </div>
              </div>
            </div>

            <div className="flex items-center justify-between p-3 bg-muted/30 rounded-lg">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-muted rounded-full flex items-center justify-center">
                  <Icon name="Minus" size={16} color="var(--color-muted-foreground)" />
                </div>
                <span className="text-sm font-medium">Break Even Trades</span>
              </div>
              <div className="text-right">
                <div className="text-sm font-data font-semibold text-muted-foreground">
                  {summary.breakEvenTrades}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Overall Performance */}
        <div className="bg-card border border-border rounded-lg p-6">
          <h3 className="text-lg font-semibold text-foreground mb-4">Overall Performance</h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground">Total Return</span>
              <span className={`text-lg font-semibold font-data ${
                summary.totalPnLPercent >= 0 ? 'text-success' : 'text-error'
              }`}>
                {summary.totalPnLPercent >= 0 ? '+' : ''}{summary.totalPnLPercent.toFixed(2)}%
              </span>
            </div>

            <div className="w-full bg-background rounded-full h-2">
              <div 
                className={`h-2 rounded-full transition-all duration-300 ${
                  summary.winRate >= 60 ? 'bg-success' : summary.winRate >= 40 ? 'bg-warning' : 'bg-error'
                }`}
                style={{ width: `${Math.min(summary.winRate, 100)}%` }}
              />
            </div>

            <div className="grid grid-cols-2 gap-4 pt-4 border-t border-border">
              <div className="text-center">
                <div className="text-2xl font-bold font-data text-success">
                  {summary.profitableTrades}
                </div>
                <div className="text-xs text-muted-foreground">Winners</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold font-data text-error">
                  {summary.lossMakingTrades}
                </div>
                <div className="text-xs text-muted-foreground">Losers</div>
              </div>
            </div>

            <div className="pt-4 border-t border-border">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Risk-Reward Ratio</span>
                <span className="text-sm font-data font-medium">
                  {summary.avgLoss !== 0 ? `1:${Math.abs(summary.avgProfit / summary.avgLoss).toFixed(2)}` : 'N/A'}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TradeSummary;