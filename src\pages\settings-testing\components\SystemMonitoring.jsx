import React, { useState, useEffect } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';
import Select from '../../../components/ui/Select';

const SystemMonitoring = () => {
  const [systemStats, setSystemStats] = useState({
    uptime: '2d 14h 32m',
    memoryUsage: 68,
    cpuUsage: 45,
    diskUsage: 32,
    networkLatency: 45,
    apiCalls: 1247,
    errorRate: 0.8
  });

  const [performanceData, setPerformanceData] = useState([
    { time: '14:30', responseTime: 120, apiCalls: 45, errors: 0 },
    { time: '14:35', responseTime: 135, apiCalls: 52, errors: 1 },
    { time: '14:40', responseTime: 98, apiCalls: 38, errors: 0 },
    { time: '14:45', responseTime: 156, apiCalls: 67, errors: 2 },
    { time: '14:50', responseTime: 89, apiCalls: 41, errors: 0 }
  ]);

  const [errorLogs, setErrorLogs] = useState([
    {
      id: 1,
      timestamp: new Date(Date.now() - 300000),
      level: 'ERROR',
      component: 'Yahoo Finance API',
      message: 'Rate limit exceeded for symbol RELIANCE',
      details: 'HTTP 429 - Too Many Requests'
    },
    {
      id: 2,
      timestamp: new Date(Date.now() - 600000),
      level: 'WARNING',
      component: 'Signal Generator',
      message: 'Low volume detected for INFY',
      details: 'Volume below threshold of 100,000'
    },
    {
      id: 3,
      timestamp: new Date(Date.now() - 900000),
      level: 'INFO',
      component: 'GTT Manager',
      message: 'Order executed successfully',
      details: 'BUY order for TCS at ₹3,234.50'
    },
    {
      id: 4,
      timestamp: new Date(Date.now() - 1200000),
      level: 'ERROR',
      component: 'Database',
      message: 'Connection timeout',
      details: 'Failed to connect after 30 seconds'
    }
  ]);

  const [logFilter, setLogFilter] = useState('ALL');
  const [autoRefresh, setAutoRefresh] = useState(true);

  const logLevels = [
    { value: 'ALL', label: 'All Levels' },
    { value: 'ERROR', label: 'Errors Only' },
    { value: 'WARNING', label: 'Warnings Only' },
    { value: 'INFO', label: 'Info Only' }
  ];

  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(() => {
      // Simulate real-time updates
      setSystemStats(prev => ({
        ...prev,
        memoryUsage: Math.max(30, Math.min(90, prev.memoryUsage + (Math.random() - 0.5) * 10)),
        cpuUsage: Math.max(20, Math.min(80, prev.cpuUsage + (Math.random() - 0.5) * 15)),
        networkLatency: Math.max(20, Math.min(200, prev.networkLatency + (Math.random() - 0.5) * 20)),
        apiCalls: prev.apiCalls + Math.floor(Math.random() * 5)
      }));
    }, 5000);

    return () => clearInterval(interval);
  }, [autoRefresh]);

  const getFilteredLogs = () => {
    if (logFilter === 'ALL') return errorLogs;
    return errorLogs.filter(log => log.level === logFilter);
  };

  const getLevelColor = (level) => {
    switch (level) {
      case 'ERROR': return 'text-error bg-error/10';
      case 'WARNING': return 'text-warning bg-warning/10';
      case 'INFO': return 'text-accent bg-accent/10';
      default: return 'text-muted-foreground bg-muted/50';
    }
  };

  const getLevelIcon = (level) => {
    switch (level) {
      case 'ERROR': return 'XCircle';
      case 'WARNING': return 'AlertTriangle';
      case 'INFO': return 'Info';
      default: return 'Circle';
    }
  };

  const getUsageColor = (usage) => {
    if (usage > 80) return 'bg-error';
    if (usage > 60) return 'bg-warning';
    return 'bg-success';
  };

  const clearLogs = () => {
    setErrorLogs([]);
  };

  const exportLogs = () => {
    const dataStr = JSON.stringify(getFilteredLogs(), null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `system-logs-${new Date().toISOString().split('T')[0]}.json`;
    link.click();
  };

  return (
    <div className="space-y-8">
      {/* System Overview */}
      <div className="bg-card rounded-lg border border-border p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-success/10 rounded-lg flex items-center justify-center">
              <Icon name="Activity" size={20} color="var(--color-success)" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-foreground">System Overview</h3>
              <p className="text-sm text-muted-foreground">Real-time system performance metrics</p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <div className={`w-2 h-2 rounded-full ${autoRefresh ? 'bg-success animate-pulse-trading' : 'bg-muted-foreground'}`} />
            <span className="text-xs text-muted-foreground">
              {autoRefresh ? 'Auto-refresh ON' : 'Auto-refresh OFF'}
            </span>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setAutoRefresh(!autoRefresh)}
              iconName={autoRefresh ? "Pause" : "Play"}
              iconPosition="left"
            >
              {autoRefresh ? 'Pause' : 'Resume'}
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-4">
          <div className="bg-muted/50 rounded-lg p-4">
            <div className="flex items-center space-x-2 mb-2">
              <Icon name="Clock" size={16} className="text-muted-foreground" />
              <span className="text-sm text-muted-foreground">Uptime</span>
            </div>
            <div className="text-lg font-data font-semibold">{systemStats.uptime}</div>
          </div>

          <div className="bg-muted/50 rounded-lg p-4">
            <div className="flex items-center space-x-2 mb-2">
              <Icon name="HardDrive" size={16} className="text-muted-foreground" />
              <span className="text-sm text-muted-foreground">Memory</span>
            </div>
            <div className="text-lg font-data font-semibold">{systemStats.memoryUsage}%</div>
            <div className="w-full bg-background rounded-full h-1 mt-2">
              <div 
                className={`h-1 rounded-full transition-all duration-300 ${getUsageColor(systemStats.memoryUsage)}`}
                style={{ width: `${systemStats.memoryUsage}%` }}
              />
            </div>
          </div>

          <div className="bg-muted/50 rounded-lg p-4">
            <div className="flex items-center space-x-2 mb-2">
              <Icon name="Cpu" size={16} className="text-muted-foreground" />
              <span className="text-sm text-muted-foreground">CPU</span>
            </div>
            <div className="text-lg font-data font-semibold">{systemStats.cpuUsage}%</div>
            <div className="w-full bg-background rounded-full h-1 mt-2">
              <div 
                className={`h-1 rounded-full transition-all duration-300 ${getUsageColor(systemStats.cpuUsage)}`}
                style={{ width: `${systemStats.cpuUsage}%` }}
              />
            </div>
          </div>

          <div className="bg-muted/50 rounded-lg p-4">
            <div className="flex items-center space-x-2 mb-2">
              <Icon name="Database" size={16} className="text-muted-foreground" />
              <span className="text-sm text-muted-foreground">Disk</span>
            </div>
            <div className="text-lg font-data font-semibold">{systemStats.diskUsage}%</div>
            <div className="w-full bg-background rounded-full h-1 mt-2">
              <div 
                className={`h-1 rounded-full transition-all duration-300 ${getUsageColor(systemStats.diskUsage)}`}
                style={{ width: `${systemStats.diskUsage}%` }}
              />
            </div>
          </div>

          <div className="bg-muted/50 rounded-lg p-4">
            <div className="flex items-center space-x-2 mb-2">
              <Icon name="Wifi" size={16} className="text-muted-foreground" />
              <span className="text-sm text-muted-foreground">Latency</span>
            </div>
            <div className="text-lg font-data font-semibold">{systemStats.networkLatency}ms</div>
          </div>

          <div className="bg-muted/50 rounded-lg p-4">
            <div className="flex items-center space-x-2 mb-2">
              <Icon name="Globe" size={16} className="text-muted-foreground" />
              <span className="text-sm text-muted-foreground">API Calls</span>
            </div>
            <div className="text-lg font-data font-semibold">{systemStats.apiCalls.toLocaleString()}</div>
          </div>

          <div className="bg-muted/50 rounded-lg p-4">
            <div className="flex items-center space-x-2 mb-2">
              <Icon name="AlertCircle" size={16} className="text-muted-foreground" />
              <span className="text-sm text-muted-foreground">Error Rate</span>
            </div>
            <div className="text-lg font-data font-semibold">{systemStats.errorRate}%</div>
          </div>
        </div>
      </div>

      {/* Performance Chart */}
      <div className="bg-card rounded-lg border border-border p-6">
        <div className="flex items-center space-x-3 mb-6">
          <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
            <Icon name="TrendingUp" size={20} color="var(--color-primary)" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-foreground">Performance Metrics</h3>
            <p className="text-sm text-muted-foreground">API response times and call volumes</p>
          </div>
        </div>

        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-border">
                <th className="text-left py-3 px-4 text-sm font-medium text-muted-foreground">Time</th>
                <th className="text-left py-3 px-4 text-sm font-medium text-muted-foreground">Response Time</th>
                <th className="text-left py-3 px-4 text-sm font-medium text-muted-foreground">API Calls</th>
                <th className="text-left py-3 px-4 text-sm font-medium text-muted-foreground">Errors</th>
                <th className="text-left py-3 px-4 text-sm font-medium text-muted-foreground">Status</th>
              </tr>
            </thead>
            <tbody>
              {performanceData.map((data, index) => (
                <tr key={index} className="border-b border-border/50">
                  <td className="py-3 px-4 text-sm font-data">{data.time}</td>
                  <td className="py-3 px-4 text-sm font-data">
                    <span className={`${data.responseTime > 150 ? 'text-warning' : 'text-success'}`}>
                      {data.responseTime}ms
                    </span>
                  </td>
                  <td className="py-3 px-4 text-sm font-data">{data.apiCalls}</td>
                  <td className="py-3 px-4 text-sm font-data">
                    <span className={`${data.errors > 0 ? 'text-error' : 'text-success'}`}>
                      {data.errors}
                    </span>
                  </td>
                  <td className="py-3 px-4">
                    <div className={`inline-flex items-center space-x-1 px-2 py-1 rounded-full text-xs ${
                      data.errors === 0 ? 'bg-success/10 text-success' : 'bg-warning/10 text-warning'
                    }`}>
                      <div className={`w-1.5 h-1.5 rounded-full ${
                        data.errors === 0 ? 'bg-success' : 'bg-warning'
                      }`} />
                      <span>{data.errors === 0 ? 'Healthy' : 'Warning'}</span>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Error Logs */}
      <div className="bg-card rounded-lg border border-border p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-error/10 rounded-lg flex items-center justify-center">
              <Icon name="FileText" size={20} color="var(--color-error)" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-foreground">System Logs</h3>
              <p className="text-sm text-muted-foreground">
                {getFilteredLogs().length} log entries
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-3">
            <Select
              options={logLevels}
              value={logFilter}
              onChange={setLogFilter}
              className="w-40"
            />
            <Button
              variant="outline"
              size="sm"
              onClick={exportLogs}
              disabled={getFilteredLogs().length === 0}
              iconName="Download"
              iconPosition="left"
            >
              Export
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={clearLogs}
              disabled={errorLogs.length === 0}
              iconName="Trash2"
              iconPosition="left"
            >
              Clear
            </Button>
          </div>
        </div>

        {getFilteredLogs().length > 0 ? (
          <div className="space-y-3 max-h-96 overflow-y-auto">
            {getFilteredLogs().map((log) => (
              <div key={log.id} className="bg-muted/50 rounded-lg p-4">
                <div className="flex items-start justify-between mb-2">
                  <div className="flex items-center space-x-3">
                    <Icon 
                      name={getLevelIcon(log.level)} 
                      size={16} 
                      className={getLevelColor(log.level).split(' ')[0]}
                    />
                    <span className={`text-xs px-2 py-1 rounded-full font-medium ${getLevelColor(log.level)}`}>
                      {log.level}
                    </span>
                    <span className="text-sm font-medium">{log.component}</span>
                  </div>
                  <span className="text-xs text-muted-foreground">
                    {log.timestamp.toLocaleString('en-IN', {
                      day: '2-digit',
                      month: '2-digit',
                      hour: '2-digit',
                      minute: '2-digit'
                    })}
                  </span>
                </div>
                <p className="text-sm text-foreground mb-1">{log.message}</p>
                {log.details && (
                  <p className="text-xs text-muted-foreground font-data">{log.details}</p>
                )}
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <Icon name="CheckCircle" size={48} className="text-success mx-auto mb-4" />
            <p className="text-muted-foreground">No logs found</p>
            <p className="text-sm text-muted-foreground">
              {logFilter === 'ALL' ? 'System is running smoothly' : `No ${logFilter.toLowerCase()} logs found`}
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default SystemMonitoring;