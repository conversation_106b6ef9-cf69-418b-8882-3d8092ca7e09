import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const EmergencyControls = ({ 
  isStrategyActive, 
  onToggleStrategy, 
  onWithdrawCapital,
  totalCapital,
  usedCapital 
}) => {
  const [showPauseConfirm, setShowPauseConfirm] = useState(false);
  const [showWithdrawConfirm, setShowWithdrawConfirm] = useState(false);
  const [withdrawAmount, setWithdrawAmount] = useState('');
  const [withdrawError, setWithdrawError] = useState('');

  const handleStrategyToggle = () => {
    if (isStrategyActive) {
      setShowPauseConfirm(true);
    } else {
      onToggleStrategy();
    }
  };

  const confirmStrategyPause = () => {
    onToggleStrategy();
    setShowPauseConfirm(false);
  };

  const handleWithdrawClick = () => {
    setShowWithdrawConfirm(true);
    setWithdrawAmount('');
    setWithdrawError('');
  };

  const handleWithdrawAmountChange = (e) => {
    const value = e.target.value;
    setWithdrawAmount(value);
    
    const numValue = parseFloat(value);
    const availableForWithdrawal = totalCapital - usedCapital;
    
    if (isNaN(numValue) || numValue <= 0) {
      setWithdrawError('Please enter a valid amount');
    } else if (numValue > availableForWithdrawal) {
      setWithdrawError(`Maximum withdrawal: ₹${availableForWithdrawal.toLocaleString('en-IN')}`);
    } else {
      setWithdrawError('');
    }
  };

  const confirmWithdrawal = () => {
    const numValue = parseFloat(withdrawAmount);
    if (!withdrawError && numValue > 0) {
      onWithdrawCapital(numValue);
      setShowWithdrawConfirm(false);
      setWithdrawAmount('');
    }
  };

  const availableForWithdrawal = totalCapital - usedCapital;

  return (
    <>
      <div className="bg-card rounded-lg border border-border p-6">
        <div className="flex items-center space-x-3 mb-6">
          <div className="w-10 h-10 bg-warning/10 rounded-lg flex items-center justify-center">
            <Icon name="Shield" size={20} color="var(--color-warning)" />
          </div>
          <div>
            <h2 className="text-lg font-semibold text-foreground">Emergency Controls</h2>
            <p className="text-sm text-muted-foreground">Strategy management and capital controls</p>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Strategy Control */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-medium text-foreground">Strategy Status</h3>
                <p className="text-sm text-muted-foreground">
                  {isStrategyActive ? 'Strategy is currently active' : 'Strategy is paused'}
                </p>
              </div>
              <div className={`w-3 h-3 rounded-full ${isStrategyActive ? 'bg-success animate-pulse-trading' : 'bg-error'}`} />
            </div>
            
            <Button
              variant={isStrategyActive ? "destructive" : "default"}
              onClick={handleStrategyToggle}
              iconName={isStrategyActive ? "Pause" : "Play"}
              iconPosition="left"
              fullWidth
            >
              {isStrategyActive ? 'Pause Strategy' : 'Resume Strategy'}
            </Button>
            
            {!isStrategyActive && (
              <div className="bg-warning/10 border border-warning/20 rounded-lg p-3">
                <div className="flex items-center space-x-2">
                  <Icon name="AlertTriangle" size={16} color="var(--color-warning)" />
                  <span className="text-sm text-warning">Strategy is currently paused</span>
                </div>
              </div>
            )}
          </div>

          {/* Capital Withdrawal */}
          <div className="space-y-4">
            <div>
              <h3 className="font-medium text-foreground">Capital Withdrawal</h3>
              <p className="text-sm text-muted-foreground">
                Available: ₹{availableForWithdrawal.toLocaleString('en-IN')}
              </p>
            </div>
            
            <Button
              variant="outline"
              onClick={handleWithdrawClick}
              disabled={availableForWithdrawal <= 0}
              iconName="ArrowDownLeft"
              iconPosition="left"
              fullWidth
            >
              Withdraw Capital
            </Button>
            
            {availableForWithdrawal <= 0 && (
              <div className="bg-muted/50 border border-border rounded-lg p-3">
                <div className="flex items-center space-x-2">
                  <Icon name="Info" size={16} color="var(--color-muted-foreground)" />
                  <span className="text-sm text-muted-foreground">No capital available for withdrawal</span>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Quick Stats */}
        <div className="mt-6 pt-6 border-t border-border">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-sm text-muted-foreground">Total Capital</div>
              <div className="text-lg font-semibold text-foreground font-data">
                ₹{totalCapital.toLocaleString('en-IN')}
              </div>
            </div>
            <div className="text-center">
              <div className="text-sm text-muted-foreground">Used Capital</div>
              <div className="text-lg font-semibold text-error font-data">
                ₹{usedCapital.toLocaleString('en-IN')}
              </div>
            </div>
            <div className="text-center">
              <div className="text-sm text-muted-foreground">Available</div>
              <div className="text-lg font-semibold text-success font-data">
                ₹{availableForWithdrawal.toLocaleString('en-IN')}
              </div>
            </div>
            <div className="text-center">
              <div className="text-sm text-muted-foreground">Usage</div>
              <div className="text-lg font-semibold text-foreground font-data">
                {totalCapital > 0 ? ((usedCapital / totalCapital) * 100).toFixed(1) : 0}%
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Strategy Pause Confirmation Modal */}
      {showPauseConfirm && (
        <div className="fixed inset-0 z-[1300] bg-black/50 flex items-center justify-center p-4">
          <div className="bg-card rounded-lg shadow-trading-lg max-w-md w-full p-6">
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-10 h-10 bg-warning/10 rounded-full flex items-center justify-center">
                <Icon name="Pause" size={20} color="var(--color-warning)" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-foreground">Pause Strategy</h3>
                <p className="text-sm text-muted-foreground">Confirm strategy pause</p>
              </div>
            </div>
            <p className="text-sm text-foreground mb-6">
              Are you sure you want to pause the trading strategy? This will:
            </p>
            <ul className="text-sm text-muted-foreground mb-6 space-y-1">
              <li>• Stop generating new buy signals</li>
              <li>• Pause automatic GTT order creation</li>
              <li>• Keep existing positions active</li>
              <li>• Require manual restart</li>
            </ul>
            <div className="flex space-x-3">
              <Button
                variant="outline"
                onClick={() => setShowPauseConfirm(false)}
                className="flex-1"
              >
                Cancel
              </Button>
              <Button
                variant="destructive"
                onClick={confirmStrategyPause}
                className="flex-1"
                iconName="Pause"
                iconPosition="left"
              >
                Pause Strategy
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Capital Withdrawal Confirmation Modal */}
      {showWithdrawConfirm && (
        <div className="fixed inset-0 z-[1300] bg-black/50 flex items-center justify-center p-4">
          <div className="bg-card rounded-lg shadow-trading-lg max-w-md w-full p-6">
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-10 h-10 bg-accent/10 rounded-full flex items-center justify-center">
                <Icon name="ArrowDownLeft" size={20} color="var(--color-accent)" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-foreground">Withdraw Capital</h3>
                <p className="text-sm text-muted-foreground">Enter withdrawal amount</p>
              </div>
            </div>
            
            <div className="space-y-4">
              <div className="bg-muted/50 rounded-lg p-3">
                <div className="text-sm text-muted-foreground">Available for withdrawal</div>
                <div className="text-lg font-semibold text-foreground font-data">
                  ₹{availableForWithdrawal.toLocaleString('en-IN')}
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-foreground mb-2">
                  Withdrawal Amount
                </label>
                <input
                  type="number"
                  placeholder="Enter amount"
                  value={withdrawAmount}
                  onChange={handleWithdrawAmountChange}
                  className="w-full px-3 py-2 border border-border rounded-md bg-input text-foreground placeholder-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring"
                />
                {withdrawError && (
                  <p className="text-sm text-error mt-1">{withdrawError}</p>
                )}
              </div>
            </div>
            
            <div className="flex space-x-3 mt-6">
              <Button
                variant="outline"
                onClick={() => setShowWithdrawConfirm(false)}
                className="flex-1"
              >
                Cancel
              </Button>
              <Button
                variant="default"
                onClick={confirmWithdrawal}
                disabled={!!withdrawError || !withdrawAmount}
                className="flex-1"
                iconName="ArrowDownLeft"
                iconPosition="left"
              >
                Withdraw
              </Button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default EmergencyControls;